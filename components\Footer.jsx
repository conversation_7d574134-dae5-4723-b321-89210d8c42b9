import React, { useRef, useState } from "react";
import WaitlistModal from "./WaitlistModal";

export function Footer() {
  const [showWaitlistModal, setShowWaitlistModal] = useState(false);
  const handleOpenWaitlist = (e) => {
    e.preventDefault();
    setShowWaitlistModal(true);
  };

  const handleCloseWaitlist = () => {
    setShowWaitlistModal(false);
  };
  return (
    <>
      {" "}
      {showWaitlistModal && <WaitlistModal onClose={handleCloseWaitlist} />}
      <div className="overflow-hidden relative w-full text-white bg-black">
        {/* Background decorative elements */}
        <div className="overflow-hidden absolute top-0 left-0 w-full h-full pointer-events-none">
          <div className="absolute top-0 z-[1]  right-0 w-full h-[992px]">
            <img
              className="w-full h-full"
              src="/footer-illustration.svg"
              alt=""
            />
          </div>
        </div>

        <div
          style={{
            background: "linear-gradient(to bottom, #000040, #000000)",
          }}
          className=" relative  pl-4 pt-20 pb-10  xl:pl-[200px] lg:pl-[40px] "
        >
          {/* Main Content */}
          <div className="text-left md:mb-16">
            <h2 className="mb-16 font-bold leading-normal sm:leading-tight">
              <span className="text-[#FFBBA3] md:text-[90px] text-[40px] block tracking-tight">
                Hey, there's
              </span>
              <span className="text-[#CACCFB] md:text-[90px] text-[40px] tracking-tight block -mt-5">
                More <span className="text-white">For you!</span>
              </span>
            </h2>

            {/* Feature Cards */}
            <div className="flex overflow-x-auto flex-nowrap gap-6 px-5 mt-12 scrollbar-hide lg:px-0 lg:flex-row">
              {/* Website Builder Card */}
              <div className="bg-[#FF6B3D] rounded-3xl p-8 h-[329px] min-w-[280px] lg:min-w-[317px] xl:min-w-[417px] md:h-[539px] z-[11] relative overflow-hidden 2xl:min-w-[550px]">
                <img
                  src="/coil-21.svg"
                  className="object-cover absolute top-0 left-0 w-full h-full"
                  alt=""
                />
                <div className="flex relative z-10 flex-col justify-between h-full">
                  <div className="flex justify-center items-center mb-auto w-[80px] h-[80px] bg-[#FF9975] rounded-full">
                    <svg
                      width="64"
                      height="64"
                      viewBox="0 0 64 64"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M22.3725 5.78662V22.9867H5.14575V21.1733C5.14575 11.5467 10.8791 5.81331 20.5058 5.81331H22.3725V5.78662Z"
                        fill="#FFEEE8"
                      />
                      <path
                        d="M58.0524 21.1465C58.0524 22.1598 57.2258 22.9598 56.2391 22.9598H40.9058V5.75977H42.6925C51.1725 5.78643 58.0524 12.6665 58.0524 21.1465Z"
                        fill="#FFEEE8"
                      />
                      <path
                        d="M22.3719 41.4663V56.8263C22.3719 57.8397 21.5452 58.6396 20.5585 58.6396H20.5319C12.0519 58.6396 5.17188 51.7596 5.17188 43.2796V41.4663H22.3719Z"
                        fill="#FFEEE8"
                      />
                      <path
                        opacity="0.4"
                        d="M22.3985 22.96H5.17188V41.4666H22.3985V22.96Z"
                        fill="#FFEEE8"
                      />
                      <path
                        d="M40.8792 22.96V39.6533C40.8792 40.6667 40.0525 41.4666 39.0658 41.4666H22.3726V22.96H40.8792Z"
                        fill="#FFEEE8"
                      />
                      <path
                        opacity="0.4"
                        d="M40.8792 5.78662H22.3726V22.9867H40.8792V5.78662Z"
                        fill="#FFEEE8"
                      />
                      <path
                        opacity="0.4"
                        d="M52.7998 55.2002L47.7332 60.2935C46.3465 61.6801 44.0532 61.6801 42.6132 60.2935L38.3999 56.0535C37.0132 54.6402 37.0132 52.3734 38.3999 50.9334L43.4932 45.8936L52.7998 55.2002Z"
                        fill="#FFEEE8"
                      />
                      <path
                        d="M60.2922 47.7335L52.7988 55.2002L43.4922 45.8935L50.9588 38.3735C52.3722 36.9868 54.6655 36.9868 56.0789 38.3735L60.2922 42.6134C61.6788 44.0267 61.6788 46.3469 60.2922 47.7335Z"
                        fill="#FFEEE8"
                      />
                    </svg>
                  </div>
                  <h3 className="mt-auto tracking-tight leading-[100%] w-[80px] text-[40px] md:text-[48px] xl:text-[56px] font-bold text-left  text-white">
                    Website Builder
                  </h3>
                </div>
              </div>

              {/* AI Powered Assistant Card */}
              <div className="bg-[#4D55F2] rounded-3xl p-8 min-w-[280px] lg:min-w-[317px] xl:min-w-[417px] h-[329px] md:h-[539px] z-[11] relative overflow-hidden">
                <img
                  src="/coil-22.svg"
                  className="object-cover absolute top-0 left-0 w-full h-full"
                  alt=""
                />
                <div className="flex z-10 flex-col justify-between h-full md:relative">
                  <div className="flex justify-center items-center mb-auto w-[80px] h-[80px] bg-[#5F66F3] rounded-full">
                    <svg
                      width="64"
                      height="64"
                      viewBox="0 0 64 64"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        opacity="0.4"
                        d="M32 58.6668C46.7276 58.6668 58.6667 46.7278 58.6667 32.0002C58.6667 17.2726 46.7276 5.3335 32 5.3335C17.2724 5.3335 5.33337 17.2726 5.33337 32.0002C5.33337 46.7278 17.2724 58.6668 32 58.6668Z"
                        fill="#F5F6FE"
                      />
                      <path
                        d="M38.6678 28.6669L37.5744 29.2269H37.5477L25.0677 35.6802L27.9211 26.7736L36.1344 23.0669L38.6678 28.6669Z"
                        fill="#F5F6FE"
                      />
                      <path
                        d="M36.1352 23.0667L27.9219 26.7733L32.0019 14L36.1352 23.0667Z"
                        fill="#F5F6FE"
                      />
                      <path
                        d="M27.92 26.7734L25.0667 35.6801L16 32.6668L21.8133 29.7601L26.6667 27.3334L27.92 26.7734Z"
                        fill="#F5F6FE"
                      />
                      <path
                        d="M48.0011 32.6666L36.0011 39.3332L35.1744 39.0666L34.9344 38.9866L25.0677 35.6799L37.5477 29.2266H37.5744L48.0011 32.6666Z"
                        fill="#F5F6FE"
                      />
                      <path
                        d="M35.1744 39.0668L34.6678 40.6668L32.0011 48.6668L25.3344 36.1868L25.0677 35.6802L34.9344 38.9868L35.1744 39.0668Z"
                        fill="#F5F6FE"
                      />
                    </svg>
                  </div>
                  <div className="mb-2 text-xs text-left text-white bg-white/10 rounded-[40px] px-3 py-1.5  absolute right-[4px] md:right-0 top-[50px] text-[14px] md:text-[18px]">
                    Vendor Matchmaking
                  </div>
                  <div className="mb-2 text-xs text-left text-white bg-white/10 rounded-[40px] px-3 py-1.5  absolute left-2 md:left-0 top-[120px] md:top-[180px] text-[14px] md:text-[18px]">
                    Help Create Gift Items
                  </div>

                  <div className="mb-2 text-xs text-left text-[14px] text-white bg-white/10 rounded-[40px] px-3 py-1.5  absolute left-[50%] top-[180px] md:top-[240px] blur-[2px] backdrop-blur-xs whitespace-nowrap md:text-[18px]">
                    Help Create Gift Items
                  </div>
                  <h3 className="mt-auto tracking-tight text-[40px] md:text-[48px] xl:text-[56px] font-bold leading-[100%] text-left  text-white">
                    AI Powered Assistant
                  </h3>
                </div>
              </div>

              {/* Vendor Marketplace Card */}
              <div className="bg-[#CBC6FF] rounded-3xl p-8 min-w-[280px] lg:min-w-[317px] xl:min-w-[417px] h-[329px] md:h-[539px] z-[11] relative overflow-hidden">
                <img
                  src="/coil-23.svg"
                  className="object-cover absolute top-0 left-0 w-full h-full"
                  alt=""
                />
                <div className="flex relative z-10 flex-col justify-between h-full">
                  <div className="flex justify-center items-center mb-auto w-[80px] h-[80px] bg-[#D5D1FF] rounded-full">
                    <svg
                      width="70"
                      height="70"
                      viewBox="0 0 70 70"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        opacity="0.4"
                        d="M59.0194 32.5451L60.1336 48.4795C60.647 55.8216 55.1049 62.197 47.7629 62.7104L24.513 64.3362C23.9012 64.379 23.2856 64.3686 22.6948 64.3297C23.6493 63.8353 24.4862 63.1886 25.2341 62.4145C25.3349 62.3273 25.409 62.242 25.4814 62.13C26.1569 61.4679 26.7148 60.6536 27.1607 59.767C28.0333 58.1021 28.4599 56.1743 28.3204 54.1792C28.1251 51.386 26.8577 48.9352 24.9681 47.1426C24.6816 46.8686 24.3684 46.5963 24.0325 46.3792C22.0569 44.8867 19.5958 44.0966 16.9357 44.2826C15.047 44.4146 13.2999 45.0448 11.8184 46.0305C11.3316 46.3319 10.8751 46.6845 10.4489 47.0886C10.272 47.2346 10.1236 47.4053 9.97512 47.5761L9.18084 36.2173C11.3551 38.2573 14.3155 39.3066 17.4279 39.0889C20.7797 38.8546 23.8813 36.9535 25.7152 34.1254C27.712 36.6857 30.915 38.1458 34.3466 37.9059C37.7516 37.6678 40.6725 35.8595 42.322 33.071C44.5545 35.5613 47.8336 36.9628 51.1323 36.7321C54.3511 36.507 57.1861 34.999 59.0194 32.5451Z"
                        fill="#7F7AB2"
                      />
                      <path
                        d="M40.1623 6.75774L24.2013 7.87385L23.6019 27.5903C23.5688 29.4104 23.9538 31.0944 24.7267 32.5908C26.5226 36.1007 30.3042 38.1886 34.3477 37.9059C38.4443 37.6194 41.8225 35.0843 43.1637 31.3283C43.5626 30.1509 43.7621 28.8004 43.6939 27.4418L43.6585 26.9364L40.1623 6.75774Z"
                        fill="#7F7AB2"
                      />
                      <path
                        opacity="0.6"
                        d="M61.0737 24.0611L59.8118 16.7178C58.1327 8.76222 54.2594 5.74506 46.4651 6.29009L36.2501 7.00439L39.6138 26.818C39.659 27.0822 39.7061 27.373 39.7414 27.8784C39.9978 29.2506 40.5126 30.4977 41.231 31.5969C43.3919 34.9744 47.2743 36.9751 51.1581 36.7035C54.6961 36.4561 57.7787 34.6633 59.5803 31.7572C61.0029 29.546 61.5331 26.8091 61.0737 24.0611Z"
                        fill="#7F7AB2"
                      />
                      <path
                        opacity="0.6"
                        d="M17.8179 8.32022C9.99697 8.86711 6.60782 12.392 6.0313 20.5855L5.82471 27.9512C5.75773 30.8162 6.72267 33.529 8.54925 35.5665C10.7551 38.0587 13.9717 39.3307 17.4299 39.0889C21.3137 38.8173 24.8799 36.2957 26.5268 32.7054C27.1347 31.4599 27.4901 30.045 27.5227 28.5992L28.1412 7.6251L17.8197 8.34685L17.8179 8.32022Z"
                        fill="#7F7AB2"
                      />
                      <path
                        d="M24.9692 47.1424C24.6827 46.8683 24.3696 46.596 24.0336 46.3789C22.058 44.8865 19.597 44.0963 16.9368 44.2823C15.0481 44.4144 13.3011 45.0445 11.8196 46.0302C11.3327 46.3316 10.8763 46.6843 10.4501 47.0883C8.13853 49.2014 6.80583 52.3152 7.04021 55.6671C7.17972 57.6622 7.87048 59.5118 8.96635 61.0391C9.23553 61.448 9.55607 61.8266 9.90134 62.1767C9.96012 62.2528 10.0435 62.3004 10.1023 62.3765C12.1776 64.5303 15.1796 65.7906 18.425 65.5637C21.1117 65.3758 23.4903 64.2204 25.2352 62.4143C25.336 62.327 25.4102 62.2417 25.4826 62.1297C26.158 61.4677 26.7159 60.6533 27.1618 59.7668C28.0345 58.1018 28.4611 56.174 28.3216 54.1789C28.1263 51.3857 26.8589 48.9349 24.9692 47.1424ZM21.8069 56.5858L19.8118 56.7253L19.9569 58.8004C20.0332 59.891 19.192 60.8587 18.1013 60.935C17.0106 61.0113 16.0429 60.17 15.9667 59.0794L15.8216 57.0044L13.8264 57.1439C12.7358 57.2201 11.7681 56.3789 11.6918 55.2883C11.6155 54.1976 12.4567 53.2299 13.5474 53.1536L15.5425 53.0141L15.4105 51.1255C15.3342 50.0348 16.1754 49.0671 17.2661 48.9909C18.3568 48.9146 19.3245 49.7558 19.4007 50.8465L19.5328 52.7351L21.5279 52.5956C22.6186 52.5193 23.5863 53.3605 23.6626 54.4512C23.7388 55.5419 22.8976 56.5096 21.8069 56.5858Z"
                        fill="#7F7AB2"
                      />
                    </svg>
                  </div>
                  <h3 className="mt-auto tracking-tight text-[40px] md:text-[48px] xl:text-[56px] font-bold leading-[100%] text-left  text-[#322D66]">
                    Vendor Marketplace
                  </h3>
                </div>
              </div>
            </div>
          </div>

          {/* Footer Bottom */}
        </div>
        <div className="pt-10 md:mt-[100px]">
          {/* Navigation Grid and Newsletter Section */}
          <div className="container overflow-hidden mx-auto rounded-xl xl:max-w-auto px-5 lg:max-w-[90%]">
            <div className="container flex justify-start mt-20 mb-10">
              <div className="text-[white] font-bold text-[24px] tracking-[-0.01px]">
                <div>
                  <span className="inline-flex relative gap-1 item">
                    <svg
                      width="29"
                      height="28"
                      viewBox="0 0 29 28"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M20.9449 0.627705C19.7215 1.11007 18.3026 2.17254 17.1581 3.46516C16.9468 2.71481 16.5653 1.96131 16.004 1.16997C15.6919 0.731745 15.1621 0.501594 14.6293 0.586718C13.7401 0.728591 13.2104 1.66495 13.5541 2.49727C14.1721 4.00112 14.1563 5.67522 13.4469 7.0908C13.0496 7.88529 12.46 8.55051 11.76 9.04234C11.7757 8.94776 11.7915 8.85948 11.8041 8.76805C12.1068 6.47917 11.4793 4.04841 10.0888 2.09372C8.7204 0.17371 6.09388 -0.538803 3.98447 0.432237C1.16246 1.73116 -0.0136398 4.6632 1.13093 7.5574C1.61335 8.78066 2.67594 10.1994 3.9687 11.3438C3.21827 11.5551 2.46468 11.9365 1.67011 12.4977C1.23183 12.8098 1.00165 13.3395 1.08679 13.8723C1.22867 14.7614 2.16514 15.291 2.99755 14.9474C4.50157 14.3295 6.17586 14.3452 7.5916 15.0546C8.38617 15.4518 9.05147 16.0414 9.54335 16.7413C9.44876 16.7255 9.36047 16.7098 9.26904 16.6972C6.9799 16.3945 4.54887 17.0219 2.59396 18.4154C0.673732 19.7837 -0.0388644 22.4099 0.932285 24.5191C2.23135 27.3408 5.16372 28.5167 8.05825 27.3723C9.28165 26.8899 10.7005 25.8275 11.8451 24.5348C12.0564 25.2852 12.4379 26.0387 12.9991 26.83C13.3113 27.2683 13.841 27.4984 14.3739 27.4133C15.263 27.2714 15.7928 26.3351 15.4491 25.5027C14.8311 23.9989 14.8468 22.3248 15.5563 20.9092C15.9536 20.1147 16.5432 19.4495 17.2432 18.9577C17.2274 19.0522 17.2117 19.1405 17.199 19.232C16.8963 21.5208 17.5238 23.9516 18.9143 25.9063C20.2828 27.8263 22.9093 28.5388 25.0187 27.5678C27.8407 26.2688 29.0168 23.3368 27.8722 20.4426C27.3898 19.2193 26.3272 17.8006 25.0344 16.6562C25.7849 16.4449 26.5385 16.0635 27.3299 15.5023C27.7682 15.1902 27.9983 14.6605 27.9132 14.1277C27.7713 13.2386 26.8349 12.709 26.0024 13.0526C24.4984 13.6705 22.8241 13.6548 21.4084 12.9454C20.6138 12.5482 19.9485 11.9586 19.4566 11.2587C19.5512 11.2745 19.6395 11.2902 19.731 11.3028C22.0201 11.6055 24.4511 10.9781 26.406 9.58775C28.3263 8.21947 29.0389 5.59325 28.0677 3.48408C26.7686 0.662384 23.8363 -0.51358 20.9417 0.630861L20.9449 0.627705ZM4.16735 6.32469C3.93402 5.73513 3.57457 4.32585 4.99976 3.48408C5.08805 3.43048 5.18579 3.38003 5.28984 3.33274C6.03082 2.99225 6.9799 3.3958 7.40241 3.98536C8.32942 5.28744 8.7677 6.95208 8.56905 8.44017C8.52176 8.79327 8.36095 9.31978 8.13077 9.92195C6.25469 9.60353 4.76643 7.85691 4.16104 6.32469H4.16735ZM6.8254 24.3362C6.23577 24.5695 4.82634 24.9289 3.98447 23.5039C3.93087 23.4156 3.88042 23.3179 3.83312 23.2138C3.49259 22.473 3.89618 21.524 4.48581 21.1015C5.78803 20.1746 7.45286 19.7364 8.94112 19.935C9.29426 19.9823 9.82082 20.1431 10.4231 20.3732C10.1046 22.2491 8.35779 23.7372 6.8254 24.3425V24.3362ZM13.5383 18.1348C13.1473 16.136 12.0248 14.3137 10.3695 13.0368C12.3685 12.6491 14.191 11.5235 15.468 9.86835C15.859 11.8672 16.9815 13.6895 18.6368 14.9663C16.6378 15.3541 14.8153 16.4796 13.5383 18.1348ZM24.8358 21.6785C25.0691 22.268 25.4286 23.6773 24.0034 24.5191C23.9151 24.5727 23.8174 24.6231 23.7133 24.6704C22.9723 25.0109 22.0233 24.6074 21.6007 24.0178C20.6737 22.7157 20.2355 21.0511 20.4341 19.563C20.4814 19.2099 20.6422 18.6834 20.8724 18.0812C22.7485 18.3996 24.2367 20.1462 24.8421 21.6785H24.8358ZM25.17 4.79246C25.5106 5.53335 25.107 6.48232 24.5173 6.90479C23.2151 7.83169 21.5503 8.26991 20.062 8.07129C19.7089 8.024 19.1823 7.86322 18.5801 7.63307C18.8985 5.75719 20.6454 4.26911 22.1778 3.66378C22.7674 3.43048 24.1768 3.07107 25.0187 4.4961C25.0723 4.58438 25.1227 4.68211 25.17 4.78615V4.79246Z"
                        fill="#9CC1FC"
                      />
                    </svg>
                    EventPark
                    <span className="min-w-[4px] w-[4px] h-[4px] min-h-[4px] rounded-full bg-[white] absolute bottom-[10px] right-[-8px]"></span>
                  </span>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-8 py-10 md:grid-cols-1 lg:grid-cols-4 xl:grid-cols-5">
              {/* Navigation Columns */}
              <div className="grid grid-cols-2 gap-8 lg:col-span-3 md:grid-cols-4">
                {/* FEATURES Column */}
                <div>
                  <h3 className="mb-4 text-sm font-medium tracking-wider text-white uppercase">
                    Features
                  </h3>
                  <ul className="space-y-3">
                    <li>
                      <a
                        href="#"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Vendor Marketplace
                      </a>
                    </li>
                    <li>
                      <a
                        href="#"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Guest Management
                      </a>
                    </li>
                    <li>
                      <a
                        href="#"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Budget Planner
                      </a>
                    </li>
                    <li>
                      <a
                        href="#"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        AI Matchmaker
                      </a>
                    </li>
                    <li>
                      <a
                        href="#"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Gift Registry
                      </a>
                    </li>
                    <li>
                      <a
                        href="#"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Pricing
                      </a>
                    </li>
                  </ul>
                </div>

                {/* SERVICES Column */}
                <div>
                  <h3 className="mb-4 text-sm font-medium tracking-wider text-white uppercase">
                    Services
                  </h3>
                  <ul className="space-y-3">
                    <li>
                      <a
                        href="/vendor"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        For Vendors
                      </a>
                    </li>
                    <li>
                      <a
                        href="#"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        For Event Planners
                      </a>
                    </li>
                    <li>
                      <span
                        onClick={handleOpenWaitlist}
                        className="text-[#AEAEB2] cursor-pointer hover:text-white text-[16px] leading-[180%]"
                      >
                        Join As Vendor
                      </span>
                    </li>
                    <li>
                      <a
                        href="#"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Plan An Event
                      </a>
                    </li>
                  </ul>
                </div>

                {/* COMPANY Column */}
                <div>
                  <h3 className="mb-4 text-sm font-medium tracking-wider text-white uppercase">
                    Company
                  </h3>
                  <ul className="space-y-3">
                    <li>
                      <a
                        href="/career"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        About Us
                      </a>
                    </li>
                    <li>
                      <a
                        href="/privacy-policy"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Privacy Policy
                      </a>
                    </li>
                    <li>
                      <a
                        href="/terms-of-service"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Terms Of Use
                      </a>
                    </li>
                    <li>
                      <a
                        href="/career"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Careers
                      </a>
                    </li>
                    <li>
                      <a
                        href="#"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Blog
                      </a>
                    </li>
                  </ul>
                </div>

                {/* SUPPORT Column */}
                <div>
                  <h3 className="mb-4 text-sm font-medium tracking-wider text-white uppercase">
                    Support
                  </h3>
                  <ul className="space-y-3">
                    <li>
                      <a
                        href="#"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Help Center
                      </a>
                    </li>
                    <li>
                      <a
                        href="/contact-us"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        Contact Us
                      </a>
                    </li>
                    <li>
                      <a
                        href="/faq"
                        className="text-[#AEAEB2] hover:text-white text-[16px] leading-[180%]"
                      >
                        FAQs
                      </a>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Newsletter Subscription */}
              {/* <div className="rounded-lg lg:col-span-2">
              <h3 className="mb-4 text-sm font-medium tracking-wider text-white uppercase">
                Subscribe to our newsletter
              </h3>
              <p className="text-[#AEAEB2] max-w-[274px] italic text-[18px] mb-4">
                Get event planning tips and updates straight to your inbox
              </p>
              <div className="flex  gap-2 min-w-fit sm:w-[320px]  lg:w-auto xl:w-[400px]  flex-row bg-[#F6F8FE]/6 p-2 w-full rounded-[64px]">
                <input
                  type="email"
                  placeholder="Enter your email address"
                  className="bg-transparent w-[130px] sm:w-auto text-[#888888] leading-[140%] rounded-md px-4 py-2 text-white flex-grow focus:outline-none focus:border-[#4D55F2]"
                />
                <button className="bg-[#9CC1FC] w-[129px] text-[14px] px-4 py-2 rounded-[60px] whitespace-nowrap hover:bg-[#9CC1FC]/80 transition-colors font-bold text-black cursor-pointer">
                  Subscribe Now!
                </button>
              </div>
            </div> */}
            </div>
          </div>

          {/* App Download Section */}

          <div className="overflow-hidden p-4 xl:p-8 container  xl:max-w-auto px-5 mx-auto  sm:max-w-[90%] bg-[white]/4 hidden rounded-[20px] h-[900px]  sm:h-[357px] ">
            <div className="grid grid-cols-1 gap-8 items-start md:grid-cols-2 lg:grid-cols-3">
              {/* App Store Badge */}
              <div className="lg:col-span-1">
                <div className="mb-6">
                  <div className="flex gap-2 items-center mb-4 bg-[#1A1A1A] rounded-[500px] p-[10px] gap-[10px] w-[195px] h-[44px]">
                    <span className=" text-sm w-[47%] text-[#999999] px-3 py-1.5 rounded-full">
                      AppStore
                    </span>
                    <div className="relative">
                      <span className=" text-sm w-47% font-bold bg-[white] text-[#000bdc] px-3 py-1.5 rounded-full font-medium">
                        PlayStore
                      </span>
                      <div className="min-h-[4px] absolute  bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
                    </div>
                  </div>
                  <h3 className="mb-3 mt-10 text-[28px] font-medium text-white leading-tight">
                    Plan Anytime, Anywhere!
                  </h3>
                  <p className="text-[#8E8E93] text-base mb-6 leading-relaxed">
                    Download the app and enjoy seamless
                    <br />
                    event planning at your fingertips
                  </p>
                </div>
                <div className="border border-[#A6A6A6] bg-black rounded  flex items-center justify-center gap-[5px] w-[150px] h-[44px]">
                  <a href="#" className="inline-block">
                    <svg
                      width="27"
                      height="30"
                      viewBox="0 0 27 30"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g filter="url(#filter0_ii_9699_7572)">
                        <path
                          d="M1.47952 1.29173C1.15942 1.63053 0.970214 2.15633 0.970214 2.83723V27.1648C0.970214 27.8468 1.15942 28.3715 1.47952 28.7103L1.56092 28.7895L15.1888 15.1616V15.001V14.8404L1.56092 1.21143L1.47952 1.29173Z"
                          fill="url(#paint0_linear_9699_7572)"
                        />
                        <path
                          d="M19.7306 19.7057L15.1887 15.1616V15.001V14.8404L19.7317 10.2974L19.834 10.3557L25.2163 13.4137C26.753 14.2871 26.753 15.716 25.2163 16.5905L19.834 19.6485L19.7306 19.7057Z"
                          fill="url(#paint1_linear_9699_7572)"
                        />
                        <g filter="url(#filter1_i_9699_7572)">
                          <path
                            d="M19.8341 19.6474L15.1877 15.001L1.47949 28.7103C1.98549 29.2471 2.82259 29.3131 3.76529 28.7785L19.8341 19.6474Z"
                            fill="url(#paint2_linear_9699_7572)"
                          />
                        </g>
                        <path
                          d="M19.8341 10.3547L3.76529 1.22466C2.82259 0.688963 1.98549 0.756063 1.47949 1.29286L15.1888 15.0022L19.8341 10.3547Z"
                          fill="url(#paint3_linear_9699_7572)"
                        />
                      </g>
                      <defs>
                        <filter
                          id="filter0_ii_9699_7572"
                          x="0.970215"
                          y="0.85498"
                          width="25.3987"
                          height="28.2927"
                          filterUnits="userSpaceOnUse"
                          color-interpolation-filters="sRGB"
                        >
                          <feFlood
                            flood-opacity="0"
                            result="BackgroundImageFix"
                          />
                          <feBlend
                            mode="normal"
                            in="SourceGraphic"
                            in2="BackgroundImageFix"
                            result="shape"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="-0.15" />
                          <feComposite
                            in2="hardAlpha"
                            operator="arithmetic"
                            k2="-1"
                            k3="1"
                          />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="shape"
                            result="effect1_innerShadow_9699_7572"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="0.15" />
                          <feComposite
                            in2="hardAlpha"
                            operator="arithmetic"
                            k2="-1"
                            k3="1"
                          />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="effect1_innerShadow_9699_7572"
                            result="effect2_innerShadow_9699_7572"
                          />
                        </filter>
                        <filter
                          id="filter1_i_9699_7572"
                          x="1.47949"
                          y="15.001"
                          width="18.3545"
                          height="14.1467"
                          filterUnits="userSpaceOnUse"
                          color-interpolation-filters="sRGB"
                        >
                          <feFlood
                            flood-opacity="0"
                            result="BackgroundImageFix"
                          />
                          <feBlend
                            mode="normal"
                            in="SourceGraphic"
                            in2="BackgroundImageFix"
                            result="shape"
                          />
                          <feColorMatrix
                            in="SourceAlpha"
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                            result="hardAlpha"
                          />
                          <feOffset dy="-0.15" />
                          <feComposite
                            in2="hardAlpha"
                            operator="arithmetic"
                            k2="-1"
                            k3="1"
                          />
                          <feColorMatrix
                            type="matrix"
                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"
                          />
                          <feBlend
                            mode="normal"
                            in2="shape"
                            result="effect1_innerShadow_9699_7572"
                          />
                        </filter>
                        <linearGradient
                          id="paint0_linear_9699_7572"
                          x1="13.9808"
                          y1="2.57983"
                          x2="-4.4794"
                          y2="21.04"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop stop-color="#00A0FF" />
                          <stop offset="0.0066" stop-color="#00A1FF" />
                          <stop offset="0.2601" stop-color="#00BEFF" />
                          <stop offset="0.5122" stop-color="#00D2FF" />
                          <stop offset="0.7604" stop-color="#00DFFF" />
                          <stop offset="1" stop-color="#00E3FF" />
                        </linearGradient>
                        <linearGradient
                          id="paint1_linear_9699_7572"
                          x1="27.2167"
                          y1="15.001"
                          x2="0.601182"
                          y2="15.001"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop stop-color="#FFE000" />
                          <stop offset="0.4087" stop-color="#FFBD00" />
                          <stop offset="0.7754" stop-color="#FFA500" />
                          <stop offset="1" stop-color="#FF9C00" />
                        </linearGradient>
                        <linearGradient
                          id="paint2_linear_9699_7572"
                          x1="17.3108"
                          y1="17.5243"
                          x2="-7.72354"
                          y2="42.5586"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop stop-color="#FF3A44" />
                          <stop offset="1" stop-color="#C31162" />
                        </linearGradient>
                        <linearGradient
                          id="paint3_linear_9699_7572"
                          x1="-1.97297"
                          y1="-6.80556"
                          x2="9.20556"
                          y2="4.37298"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop stop-color="#32A071" />
                          <stop offset="0.0685" stop-color="#2DA771" />
                          <stop offset="0.4762" stop-color="#15CF74" />
                          <stop offset="0.8009" stop-color="#06E775" />
                          <stop offset="1" stop-color="#00F076" />
                        </linearGradient>
                      </defs>
                    </svg>
                  </a>
                  <div className="flex flex-col">
                    <span className="text-[12px] leading-[100%] ">
                      GET IT ON
                    </span>
                    <span className="leading-[100%]">Google Play</span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col justify-center items-center sm:hidden lg:col-span-1">
                {/* Using the QR code SVG file */}
                <div className="w-[150px] h-[150px] xl:w-[237px] xl:h-[237px]">
                  <img
                    src="/qr-code.svg"
                    alt="QR Code"
                    className="w-full h-full"
                  />
                </div>
                <p className="text-base font-medium text-[#F6F8FE] mt-[36px] tracking-wider text-center text-white">
                  SCAN TO DOWNLOAD THE APP
                </p>
              </div>

              {/* Phone Images */}
              <div className="flex justify-center lg:col-span-1 relative sm:ml-[-50%]">
                <div className="relative w-[180px] lg:w-[200px] lg:h-auto xl:w-[300px] xl:h-[400px]">
                  {/* Using the SVG files for the phone screens */}
                  <div className="relative z-10">
                    <img
                      src="/screen-1.svg"
                      alt="Phone screen"
                      className="w-auto h-[400px]"
                    />
                  </div>
                  <div className="absolute right-[-40px] sm:right-[-100px] xl:right-[-150px] transform  -bottom-15 z-10">
                    <img
                      src="/screen2.svg"
                      alt="Phone screen"
                      className="xl:w-[250px] w-[160px] h-[400px]"
                    />
                  </div>
                </div>
              </div>

              {/* QR Code */}
              <div className="hidden flex-col justify-center items-center sm:flex lg:col-span-1">
                {/* Using the QR code SVG file */}
                <div className="w-[150px] h-[150px] xl:w-[237px] xl:h-[237px]">
                  <img
                    src="/qr-code.svg"
                    alt="QR Code"
                    className="w-full h-full"
                  />
                </div>
                <p className="text-base font-medium text-[#F6F8FE] mt-[36px] tracking-wider text-center text-white">
                  SCAN TO DOWNLOAD THE APP
                </p>
              </div>
            </div>
          </div>

          {/* Large EventPark Logo */}
          <div className="relative lg:h-[240px] xl:h-[340px] left-[-10%] sm:top-[-100px] xl:top-[-135px]">
            <div className="text-[white]/15 lg:h-[512px] font-bold text-[85px] sm:text-[100px] lg:text-[240px] xl:text-[360px] tracking-[-6px] leading-[160%] tracking-tighter opacity-20 text-center overflow-hidden">
              EventPark
            </div>
          </div>

          {/* EventPark Logo */}

          {/* Social Links */}
          <div className="w-full border-t border-t-[#1A1A1A]">
            <div className="grid flex-wrap grid-cols-2 gap-6 justify-center items-center py-8 pl-6 space-x-6 md:flex">
              <a
                target="_blank"
                href="https://www.instagram.com/eventparkafrica"
                className="font-medium text-[#AEAEB2] text-sm md:text-base uppercase tracking-wider hover:text-[#CACCFB]"
              >
                Instagram
              </a>
              <span className="text-[#9CC1FC] hidden md:block">•</span>
              <a
                target="_blank"
                href="https://twitter.com/EventParkAfrica"
                className="font-medium text-[#AEAEB2] text-sm md:text-base uppercase tracking-wider hover:text-[#CACCFB]"
              >
                X (Twitter)
              </a>
              <span className="text-[#9CC1FC] hidden md:block">•</span>
              <a
                target="_blank"
                href="https://vm.tiktok.com/ZMBWP9YLU/"
                className="font-medium text-[#AEAEB2] text-sm md:text-base uppercase tracking-wider hover:text-[#CACCFB]"
              >
                TikTok
              </a>
              <span className="text-[#9CC1FC] hidden md:block">•</span>
              <a
                target="_blank"
                href="https://www.linkedin.com/company/eventpark-ltd"
                className="font-medium text-[#AEAEB2] text-sm md:text-base uppercase tracking-wider hover:text-[#CACCFB]"
              >
                LinkedIn
              </a>
            </div>
          </div>

          <div className="bg-[#1a1a1a] py-[24px] w-full">
            {/* Copyright */}
            <div className="text-[#969696] text-base text-center">
              EventPark © Copyright — {new Date().getFullYear()}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
