import React from "react";

export function Features() {
  return (
    <div className="py-12 w-full md:py-16 lg:py-20">
      <div className="container px-4 mx-auto max-w-7xl">
        {/* Features Header */}
        <div className="container mx-auto mb-16 text-left">
          <h2 className="font-bold md:leading-tight">
            <span
              className="text-[40px] sm:text-[60px] lg:text-[90px]"
              style={{
                background:
                  "linear-gradient(to right, #FFBBA3 0%, #A6AAF9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",

                display: "block",
                marginBottom: "-20px",
              }}
            >
              Features
            </span>
            <span
              className="text-[48px] sm:text-[70px] lg:text-[100px]"
              style={{
                color: "#000059",

                display: "block",
              }}
            >
              For you!
            </span>
          </h2>
        </div>

        {/* Features Grid */}
        <div className="container mx-auto grid relative grid-cols-1 gap-6 mb-10 md:grid-cols-[40.5%_57%]">
          {/* Guest List Feature */}
          <div
            className="overflow-hidden h-[263px] md:min-h-[539px] rounded-3xl w-[100%] relative shadow-sm"
            style={{
              background:
                "linear-gradient(to bottom right, #FEF7F4 0%, #FEFEEB 100%)",
            }}
          >
            <img
              src="/coil3.svg"
              className="absolute w-full h-full md:object-cover"
              alt=""
            />
            <div className="relative z-10 p-10">
              <div className="flex flex-col space-y-6">
                <div className="md:w-[220px] w-[144px] h-[230px] md:h-[220px] rounded-t-[24px] rotate-[355deg] absolute top-[40px] left-[70px] md:max-w-[220px] z-[2] md:max-h-[220px]">
                  <img
                    src="/img3.jpg"
                    alt="Event guests"
                    className="w-full h-[88px] md:h-[138px] rounded-t-[24px] object-cover"
                  />

                  <div
                    className="bg-white pb-[10px] md:pb-[28px] pl-4 pt-4 rounded-b-[8px]"
                    style={{
                      boxShadow: "0px 2px 20px rgba(0, 0, 0, 0.08)",
                    }}
                  >
                    <h3 className="font-bold text-[#333]">Bisola's Wedding</h3>
                    <p className="text-sm text-gray-600">200 Guests</p>
                  </div>
                </div>
                <div className="md:w-[220px] w-[144px] h-[auto] md:h-[220px] absolute top-[106px] md:top-[200px] md:right-[-40px] right-[40px] rounded-t-[24px] md:max-w-[220px] md:max-h-[220px] rotate-[7deg]">
                  {" "}
                  <img
                    src="/img3.png"
                    alt="Event celebration"
                    className="w-full h-[88px] md:h-[138px] rounded-t-[24px] object-cover"
                  />
                  <div
                    className="bg-white pb-[10px] md:pb-[28px] pl-4 pt-4 rounded-b-[8px]"
                    style={{
                      boxShadow: "0px 2px 20px rgba(0, 0, 0, 0.08)",
                    }}
                  >
                    <h3 className="font-bold text-[#333]">Temi's Birthday</h3>
                    <p className="text-sm text-gray-600">100 Guests</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Guest & RSVP Manager Feature */}
          <div
            className="overflow-hidden relative md:min-h-[330px] rounded-3xl shadow-sm"
            style={{
              background:
                "linear-gradient(to bottom right, #F5F9FF 0%, #F5F4FF 100%)",
            }}
          >
            <img
              src="/coil4.svg"
              className="object-cover absolute w-full h-full"
              alt=""
            />
            <div className="relative z-10 p-[16px] md:p-10">
              <div
                className="text-[rgba(127,127,127,0.5)] uppercase text-[14pxs] md:text-[18px] tracking-[1px] font-medium mb-4"
                style={{
                  background:
                    "linear-gradient(to right, #7F7F7F80 0%, #3d3d3d 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                }}
              >
                Guest & RSVP Manager
              </div>
              <h3 className="text-[#1D427D] text-[28px] sm:text-[32px] md:text-[48px] tracking-tighter md:tracking-tight font-medium leading-[130%] mb-2 sm:mb-4">
                Organize & Track Your
                <br />
                <span className="text-[#9CC1FC]">Guests Effortlessly</span>
              </h3>
              <p className="mb-8  text-[16px] sm:text-[24px] leading-[140%] text-[#8E8E93]">
                Easily invite guests, track responses,
                <br />
                and manage seating
              </p>
              <a target="_blank" href="#">
                <button className="bg-[white] w-[170px] md:w-[202px] h-[48px] md:h-[56px] shadow-[0px_12px_120px_0_rgba(95,95,95,0.06)] hover:text-[hsl(217,94%,15%)] transition-colors duration-300 ease-out text-[#032863] px-4 py-3 rounded-full font-bold leading-7 hover:bg-opacity-90  cursor-pointer flex justify-center items-center">
                  Manage My Guests
                </button>
              </a>
            </div>
            <div className="absolute -top-10 -left-10 w-32 h-32 opacity-70">
              <img
                src="/coiled.svg"
                alt=""
                className="w-full h-full rotate-180"
              />
            </div>
          </div>
        </div>

        {/* Gift Registry Feature */}
        <div
          className="overflow-hidden mx-auto container rounded-3xl h-[551px] sm:h-[605px]"
          style={{
            background: "linear-gradient(to right, #00000D 0%, #000059 100%)",

            position: "relative",
          }}
        >
          <div className="flex flex-col mt-[70px] sm:mt-0 gap-2 items-center p-4 sm:p-10">
            <div className="relative z-10 text-white">
              <div className="mb-4 font-medium text-center leading-[100%] text-[#FFCCBA] tracking-[2px] uppercase sm:text-[18px] text-[16px] mt-4">
                Gift Registry
              </div>
              <h3 className="sm:ext-[64px] text-[32px] font-medium text-center leading-[100%] tracking-tight mb-4 text-[#E8ECF1]">
                Create the
                <br />
                Perfect gift list
              </h3>
              <p className="mb-8  text-[#E6E6E6] text-center leading-[140%] text-[20px] font-normal tracking-tight">
                Create and share a wishlist or
                <br />
                receive cash gifts.
              </p>
            </div>
            <div className="flex relative bottom-[0px] sm:bottom-[0px] z-10 justify-center items-center">
              <img
                src="/original-gift.png"
                alt="Gift box"
                className="object-cover h-[340px]  w-[510px] sm:h-auto sm:rotate-[11deg] rotate-[10deg]"
              />
            </div>
          </div>

          <svg
            className="absolute bottom-0"
            width="1040"
            height="605"
            viewBox="0 0 1040 605"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g filter="url(#filter0_f_7869_145673)">
              <circle cx="213" cy="856" r="450" fill="#B8BBFA" />
            </g>
            <defs>
              <filter
                id="filter0_f_7869_145673"
                x="-737"
                y="-94"
                width="1900"
                height="1900"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend
                  mode="normal"
                  in="SourceGraphic"
                  in2="BackgroundImageFix"
                  result="shape"
                />
                <feGaussianBlur
                  stdDeviation="250"
                  result="effect1_foregroundBlur_7869_145673"
                />
              </filter>
            </defs>
          </svg>

          <svg
            className="absolute right-0 bottom-0"
            width="983"
            height="605"
            viewBox="0 0 983 605"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g filter="url(#filter0_f_7869_145769)">
              <circle cx="950" cy="841" r="450" fill="#FF9975" />
            </g>
            <defs>
              <filter
                id="filter0_f_7869_145769"
                x="0"
                y="-109"
                width="1900"
                height="1900"
                filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB"
              >
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend
                  mode="normal"
                  in="SourceGraphic"
                  in2="BackgroundImageFix"
                  result="shape"
                />
                <feGaussianBlur
                  stdDeviation="250"
                  result="effect1_foregroundBlur_7869_145769"
                />
              </filter>
            </defs>
          </svg>

          {/* Background decorative elements */}
          <div className="absolute bottom-0 w-full">
            <img src="/gift-coil.svg" alt="" className="w-full h-full" />
          </div>
        </div>

        {/* Budget Planner Feature */}
        <div className="md:grid mt-[160px] flex flex-col-reverse md:grid-cols-[58%_40%] h-[595px] mx-auto gap-6 container justify-between md:mt-10 mb-8">
          <div
            className="overflow-hidden h-[368px] md:h-auto min-h-[330px] relative rounded-3xl shadow-sm"
            style={{
              background:
                "linear-gradient(to bottom right, #F5F9FF 0%, #F5F9FF 100%)",
            }}
          >
            <img
              src="/coil6.svg"
              className="object-cover absolute w-[90%] h-full"
              alt=""
            />
            <div className="relative z-10 p-8 md:p-10">
              <div
                className="text-[rgba(127,127,127,0.5)] uppercase text-[14px] sm:text-[18px] tracking-[1px] md:pt-5 font-medium mb-4"
                style={{
                  background:
                    "linear-gradient(to right, #7F7F7F80 0%, #3d3d3d 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                }}
              >
                Budget Planner & Tracker
              </div>
              <h3 className="tracking-[-1px] font-medium leading-[130%] mb-4">
                <span
                  className="lg:text-[48px] leading-[190%] text-[27px] sm:text-[30px] md:text-[40px] xl:leading-[190%] tracking-tight font-medium"
                  style={{
                    color: "#0109A5",
                  }}
                >
                  Stay on Top of Your
                </span>
                <br />
                <span
                  className="sm:text-[40px] text-[27px] md:text-[48px]"
                  style={{
                    background:
                      "linear-gradient(to right, #0109A5 0%, #A6AAF9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                  }}
                >
                  Event Finances
                </span>
              </h3>
              <p className="sm:mb-8 mb-8 text-[16px] sm:text-[20px] md:text-[22px] lg:text-[24px] leading-[140%] tracking-tight text-[#8E8E93]">
                Track expenses, set event budgets, and
                <br />
                manage your spending with ease.
              </p>
              <button className="bg-[white] w-[150px] font-bold  flex justify-center items-center shadow-[0px_12px_120px_0_rgba(95,95,95,0.06)] text-[#4D55F2] px-4 py-3 h-[56px]  rounded-full  leading-7 hover:bg-opacity-90 transition">
                Plan a Budget
              </button>
            </div>
          </div>

          {/* Budget Visualization */}
          <div className="relative h-[364px] sm:h-auto">
            <div className="absolute hidden sm:block bottom-[-40px] z-[10] w-[147px] rotate-[20deg] sm:right-[90px] md:right-[140px]">
              <div className="p-2  bg-white rotate-[0deg] rounded-lg flex flex-col justify-center items-center h-[77px] min-h-[74px] shadow-md">
                <div className="text-[14px] font-bold">₦1,650,000</div>
                <div className="text-[12px] flex items-center">
                  <span className="w-2 h-2 rounded-full bg-[#666666] mr-1"></span>
                  Venue
                </div>
              </div>
            </div>

            <div
              className="overflow-hidden relative h-full rounded-3xl"
              style={{
                background:
                  "linear-gradient(to bottom right, #FFFCFB 100%, #FEF5F1 100%)",
                minHeight: "330px",
              }}
            >
              <div className="relative z-10 p-10">
                <img
                  src="/coil7.svg"
                  className="object-cover absolute right-10 min-h-full"
                  alt=""
                />
                <img
                  src="/coil8.svg"
                  className="object-scale-down absolute top-[-120px] sm:top-[-64px] right-[-80px] min-h-full"
                  alt=""
                />

                {/* Budget visualization with curved line and budget items */}
                <div className="relative h-[300px]">
                  {/* Curved budget line (simplified representation) */}
                  <div className="absolute w-full h-full">
                    {/* <svg
                    className="rotate-[90deg]"
                    width="100%"
                    height="100%"
                    viewBox="0 0 500 300"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0,150 C100,50 400,250 500,100"
                      stroke="#FFD6C4"
                      strokeWidth="40"
                      fill="none"
                    />
                  </svg> */}
                  </div>

                  {/* Budget items */}
                  <div className="absolute top-[-10px] w-[147px]  right-[40px]">
                    <div className="p-2 bg-white rotate-[-11deg] rounded-lg flex flex-col justify-center items-center h-[77px] min-h-[74px] shadow-md">
                      <div className="text-[14px] font-bold">₦1,650,000</div>
                      <div className="text-[12px] flex items-center">
                        <span className="w-2 h-2 rounded-full bg-[#FF6B00] mr-1"></span>
                        Catering
                      </div>
                    </div>
                  </div>

                  <div className="absolute top-[180px] sm:top-[240px] w-[147px] md:right-[100px] lg:right-[220px]">
                    <div className="p-2  bg-white rotate-[0deg] rounded-lg flex flex-col justify-center items-center h-[77px] min-h-[74px] shadow-md ">
                      <div className="text-[14px] font-bold">₦1,650,000</div>
                      <div className="text-[12px] flex items-center">
                        <span className="w-2 h-2 rounded-full bg-[#9900FF] mr-1"></span>
                        Entertainment
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
