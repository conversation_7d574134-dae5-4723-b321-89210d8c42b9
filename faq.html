<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>EventPark - Frequently Asked Questions</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Rethink+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Rethink Sans', sans-serif;
    }

    body {
      background-color: #FAFAFA;
      color: #333333;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Header */
    header {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px 0;
      position: relative;
    }

    .logo {
      display: flex;
      align-items: center;
    }

    .logo img {
      height: 32px;
    }

    /* Hero Section */
    .hero {
      background-image: url('/faqhero.svg');
      background-size: cover;
      background-position: center;
      padding: 80px 0;
      text-align: center;
      margin-bottom: 40px;
    }

    .hero h1 {
      font-size: 48px;
      font-weight: 700;
      margin-bottom: 20px;
      color: #4D2C1B;
    }

    .hero h1 span {
      color: #C9A99D;
    }

    .hero p {
      font-size: 18px;
      color: #666666;
      max-width: 600px;
      margin: 0 auto;
    }

    /* Navigation */
    .nav-container {
      background-color: #FFFFFF;
      border-radius: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      margin-bottom: 40px;
    }

    nav {
      display: flex;
      justify-content: center;
      padding: 20px 0;
    }

    nav ul {
      display: flex;
      list-style: none;
      gap: 40px;
    }

    nav a {
      text-decoration: none;
      color: #333333;
      font-weight: 500;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      transition: color 0.3s;
    }

    nav a:hover {
      color: #4D55F2;
    }

    /* FAQ Tabs */
    .faq-tabs {
      display: flex;
      justify-content: center;
      margin-bottom: 30px;
    }

    .tab {
      padding: 8px 16px;
      margin: 0 5px;
      border-radius: 20px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s;
    }

    .tab.active {
      background-color: #4D55F2;
      color: white;
    }

    /* FAQ Section */
    .faq-section {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .faq-item {
      border-bottom: 1px solid #EEEEEE;
      margin-bottom: 20px;
    }

    .faq-question {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      cursor: pointer;
      color: #4D55F2;
      font-weight: 500;
      font-size: 16px;
    }

    .faq-question:hover {
      color: #1A22BF;
    }

    .faq-answer {
      padding: 0 0 16px 0;
      color: #666666;
      line-height: 1.6;
      font-size: 14px;
    }

    .faq-toggle {
      width: 24px;
      height: 24px;
      position: relative;
    }

    .faq-toggle::before,
    .faq-toggle::after {
      content: '';
      position: absolute;
      background-color: #4D55F2;
      transition: transform 0.3s;
    }

    .faq-toggle::before {
      width: 2px;
      height: 16px;
      top: 4px;
      left: 11px;
    }

    .faq-toggle::after {
      width: 16px;
      height: 2px;
      top: 11px;
      left: 4px;
    }

    .faq-item.active .faq-toggle::before {
      transform: rotate(90deg);
    }

    .faq-item.active .faq-question {
      color: #1A22BF;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .hero h1 {
        font-size: 36px;
      }

      nav ul {
        gap: 20px;
      }
    }

    @media (max-width: 480px) {
      .hero h1 {
        font-size: 28px;
      }

      nav ul {
        flex-direction: column;
        align-items: center;
        gap: 15px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header -->
    <header>
      <div class="logo">
        <img src="https://content.eventpark.africa/email/Logo.png" alt="EventPark">
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
      <h1>Got <span>Questions?</span></h1>
      <p>We answered some questions so you don't have to ask again</p>
    </section>

    <!-- Navigation -->
    <div class="nav-container">
      <nav>
        <ul>
          <li><a href="#">HOME</a></li>
          <li><a href="#">COMPANY</a></li>
          <li><a href="#">FOR VENDORS</a></li>
          <li><a href="#">MARKETPLACE</a></li>
        </ul>
      </nav>
    </div>

    <!-- FAQ Tabs -->
    <div class="faq-tabs">
      <div class="tab active">For Event Planners</div>
      <div class="tab">For Vendors</div>
    </div>

    <!-- FAQ Section -->
    <div class="faq-section">
      <div class="faq-item active">
        <div class="faq-question">
          What is EventPark?
          <div class="faq-toggle"></div>
        </div>
        <div class="faq-answer">
          EventPark is an event management platform that streamlines the process of finding, booking, and managing vendors for events.
        </div>
      </div>

      <div class="faq-item">
        <div class="faq-question">
          How do I book a vendor through EventPark?
          <div class="faq-toggle"></div>
        </div>
        <div class="faq-answer" style="display: none;">
          You can browse our marketplace, filter vendors by category, location, and price range, then request a booking directly through our platform. Our system handles all the communication and payment processing.
        </div>
      </div>

      <div class="faq-item">
        <div class="faq-question">
          How do I search for vendors on EventPark?
          <div class="faq-toggle"></div>
        </div>
        <div class="faq-answer" style="display: none;">
          Use our search function and filters to find vendors based on service type, location, price range, availability, and ratings. You can also save your favorite vendors for future reference.
        </div>
      </div>

      <div class="faq-item">
        <div class="faq-question">
          What types of events can I plan on EventPark?
          <div class="faq-toggle"></div>
        </div>
        <div class="faq-answer" style="display: none;">
          EventPark supports all types of events including weddings, corporate events, birthday parties, conferences, product launches, and more. Our platform is flexible enough to accommodate events of any size and complexity.
        </div>
      </div>

      <div class="faq-item">
        <div class="faq-question">
          Is there a fee to use EventPark?
          <div class="faq-toggle"></div>
        </div>
        <div class="faq-answer" style="display: none;">
          Basic event planning features are free for event planners. We charge a small service fee on vendor bookings, which helps us maintain the platform and provide customer support.
        </div>
      </div>

      <div class="faq-item">
        <div class="faq-question">
          How does payment work?
          <div class="faq-toggle"></div>
        </div>
        <div class="faq-answer" style="display: none;">
          EventPark offers secure payment processing. You can pay vendors through our platform using credit/debit cards or bank transfers. We hold payments in escrow until services are delivered to ensure your satisfaction.
        </div>
      </div>
    </div>
  </div>

  <script>
    // Simple JavaScript to toggle FAQ items
    document.querySelectorAll('.faq-question').forEach(question => {
      question.addEventListener('click', () => {
        const item = question.parentNode;
        const answer = question.nextElementSibling;
        
        // Toggle active class
        item.classList.toggle('active');
        
        // Toggle display of answer
        if (answer.style.display === 'none' || !answer.style.display) {
          answer.style.display = 'block';
        } else {
          answer.style.display = 'none';
        }
      });
    });

    // Tab switching
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        // Remove active class from all tabs
        document.querySelectorAll('.tab').forEach(t => {
          t.classList.remove('active');
        });
        
        // Add active class to clicked tab
        tab.classList.add('active');
      });
    });
  </script>
</body>
</html>
