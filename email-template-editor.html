<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EventPark - Email Template Editor</title>
    <style type="text/css">
      /* Reset styles */
      body,
      p,
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 0;
        padding: 0;
      }
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333333;
        background-color: #f5f5f5;
      }
      img {
        border: 0;
        display: block;
        max-width: 100%;
      }
      .main-table {
        width: 100%;
        max-width: 600px;
        margin: 0 auto;
        background-color: #fafafa;
      }
      .content-table {
        width: 100%;
        padding: 20px;
      }
      .header {
        padding: 20px 0;
      }
      .welcome-text {
        font-size: 12px;
        color: #444547;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
      .main-heading {
        font-size: 24px;
        font-weight: bold;
        color: #1a22bf; /* EventPark blue */
        margin: 10px 0 20px;
      }
      .user-greeting {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 15px;
      }
      .message-text {
        font-size: 14px;
        color: #414651;
        margin-bottom: 20px;
      }
      .share-section {
        margin: 25px 0;
        font-size: 14px;
      }
      .share-link {
        color: #4d55f2;
        text-decoration: underline;
      }
      .footer {
        padding: 20px 0;
        font-size: 12px;
        color: #999999;
        text-align: center;
      }
      .footer-heading {
        font-size: 12px;
        color: #444547;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 15px;
      }
      .social-icons {
        margin: 15px 0;
      }
      .social-icon {
        display: inline-block;
        margin: 0 5px;
      }
      .profile-image {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
      }
      .early-access-badge {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #8288f6; /* EventPark light blue */
        color: white;
        text-align: center;
        line-height: 50px;
        font-size: 24px;
        font-weight: bold;
        margin-left: auto;
      }
      .unsubscribe {
        color: #999999;
        font-size: 11px;
        margin-top: 10px;
      }
      .unsubscribe a {
        color: #4d55f2;
        text-decoration: underline;
      }

      /* Editor specific styles */
      .editor-controls {
        background-color: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
      }
      .section-editor {
        margin-bottom: 10px;
        padding: 8px;
        border: 1px dashed #ccc;
        border-radius: 4px;
        position: relative;
      }
      .section-editor:hover {
        background-color: #f0f8ff;
      }
      .edit-button {
        position: absolute;
        right: 5px;
        top: 5px;
        background-color: #4d55f2;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 3px 8px;
        font-size: 12px;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.2s;
      }
      .section-editor:hover .edit-button {
        opacity: 1;
      }
      .editable-field {
        border: 1px dashed transparent;
        padding: 2px 4px;
        border-radius: 3px;
        transition: all 0.2s;
      }
      .editable-field:hover {
        border-color: #8288f6;
        background-color: rgba(130, 136, 246, 0.05);
      }
      .category-selector {
        margin: 10px 0;
        padding: 8px;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        width: 100%;
      }
      .template-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
      }
      .action-button {
        background-color: #1a22bf;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        cursor: pointer;
        font-weight: bold;
      }
      .action-button.secondary {
        background-color: #8288f6;
      }
      .layer-name {
        font-weight: bold;
        margin-bottom: 5px;
        color: #1a22bf;
      }
    </style>
  </head>
  <body>
    <!-- Template Editor Controls -->
    <div class="editor-controls">
      <h2 style="margin-bottom: 15px; color: #1a22bf;">Email Template Editor</h2>
      
      <div class="section-editor">
        <div class="layer-name">Template Name</div>
        <input type="text" value="Early Access Confirmation" style="width: 100%; padding: 8px; margin-bottom: 10px;">
      </div>
      
      <div class="section-editor">
        <div class="layer-name">Email Category</div>
        <select class="category-selector">
          <option>Welcome Email</option>
          <option>Confirmation</option>
          <option>Newsletter</option>
          <option>Promotion</option>
          <option>Event Invitation</option>
        </select>
      </div>
      
      <div class="section-editor">
        <div class="layer-name">Sections</div>
        <div style="margin: 5px 0; padding: 5px; background: #f5f6fe; border-radius: 3px;">
          <input type="checkbox" checked> Header with Logo
        </div>
        <div style="margin: 5px 0; padding: 5px; background: #f5f6fe; border-radius: 3px;">
          <input type="checkbox" checked> Welcome Message
        </div>
        <div style="margin: 5px 0; padding: 5px; background: #f5f6fe; border-radius: 3px;">
          <input type="checkbox" checked> User Profile Section
        </div>
        <div style="margin: 5px 0; padding: 5px; background: #f5f6fe; border-radius: 3px;">
          <input type="checkbox" checked> Share Section
        </div>
        <div style="margin: 5px 0; padding: 5px; background: #f5f6fe; border-radius: 3px;">
          <input type="checkbox" checked> Footer
        </div>
      </div>
      
      <div class="template-actions">
        <button class="action-button">Save Template</button>
        <button class="action-button secondary">Preview</button>
      </div>
    </div>

    <!-- Email Template Preview -->
    <table class="main-table" cellpadding="0" cellspacing="0" border="0">
      <tr>
        <td>
          <table class="content-table" cellpadding="0" cellspacing="0" border="0">
            <!-- Header with logo and bunting -->
            <tr>
              <td class="header section-editor">
                <button class="edit-button">Edit</button>
                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                  <tr>
                    <td width="50%">
                      <img src="Logo.png" alt="EventPark" width="120" />
                    </td>
                    <td width="50%" align="right">
                      <img src="email-illustration.png" alt="Decorative bunting" width="100" />
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Welcome message -->
            <tr>
              <td style="padding: 20px 0" class="section-editor">
                <button class="edit-button">Edit</button>
                <p class="welcome-text editable-field">WELCOME TO EVENTPARK</p>
                <h1 class="main-heading editable-field">You're In! Early Access Confirmed 🚀</h1>
              </td>
            </tr>

            <!-- User profile section -->
            <tr>
              <td class="section-editor">
                <button class="edit-button">Edit</button>
                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                  <tr>
                    <td style="padding-bottom: 20px">
                      <table width="100%" cellpadding="0" cellspacing="0" border="0">
                        <tr>
                          <td width="60">
                            <div class="profile-image" style="background-color: #f0f0f0">
                              <!-- User profile image would go here -->
                            </div>
                          </td>
                          <td style="padding-left: 15px">
                            <p class="user-greeting editable-field">Hi Emmanuel,</p>
                          </td>
                          <td align="right">
                            <div class="early-access-badge editable-field">T</div>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <p class="message-text editable-field">
                        Thank you for joining the EventPark! You're now one step
                        closer to experiencing seamless ways to plan events and
                        connect with like minds.
                      </p>
                      <p class="message-text editable-field">
                        We'll keep you updated on our launch and give you
                        exclusive access when we go live. In the meantime, stay
                        tuned for exciting updates!
                      </p>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <!-- Share section -->
            <tr>
              <td class="section-editor">
                <button class="edit-button">Edit</button>
                <div class="share-section">
                  <p class="editable-field">
                    Share EventPark with your friends and move up the list!
                    Excited to have you onboard.
                  </p>
                  <p style="margin-top: 10px">
                    <a href="https://eventpark.com/invite/1a2b3c" class="share-link editable-field">
                      https://eventpark.com/invite/1a2b3c
                    </a>
                  </p>
                </div>
              </td>
            </tr>

            <!-- Footer -->
            <tr>
              <td class="section-editor">
                <button class="edit-button">Edit</button>
                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                  <tr>
                    <td class="footer">
                      <p class="footer-heading editable-field">FROM THE TEAM AT EVENTPARK</p>

                      <!-- Logo -->
                      <img src="Logo.png" alt="EventPark" width="100" style="margin: 15px auto" />

                      <!-- Copyright -->
                      <p style="margin-bottom: 15px" class="editable-field">© 2023 EventPark</p>

                      <!-- Subscription management -->
                      <p class="unsubscribe editable-field">
                        You are receiving this email because you signed up for
                        the EventPark waitlist. Updates, product announcements,
                        and event invitations will be sent to this address.<br />
                        <a href="#">Unsubscribe</a> or
                        <a href="#">manage preferences</a> at any time.
                      </p>

                      <!-- Social icons -->
                      <div class="social-icons">
                        <a href="#" class="social-icon"><img src="facebook.png" alt="Facebook" width="24" /></a>
                        <a href="#" class="social-icon"><img src="instagram.png" alt="Instagram" width="24" /></a>
                        <a href="#" class="social-icon"><img src="linkedin.png" alt="LinkedIn" width="24" /></a>
                        <a href="#" class="social-icon"><img src="tiktok.png" alt="TikTok" width="24" /></a>
                        <a href="#" class="social-icon"><img src="twitter.png" alt="Twitter" width="24" /></a>
                      </div>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>
