{"name": "eventparkwebsite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.4", "axios": "^1.9.0", "lucide-react": "^0.508.0", "react": "^19.0.0", "react-dom": "^19.0.0", "swiper": "^11.2.6", "tailwindcss": "^4.1.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "framer-motion": "^12.9.2", "globals": "^16.0.0", "react-router-dom": "^7.5.3", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vike": "^0.4.229", "vike-react": "^0.6.1", "vite": "^6.3.1", "vite-plugin-ssr": "^0.4.142"}}