import React, { useState } from "react";

export function FAQSection() {
  // State to track which FAQ item is open
  const [openIndex, setOpenIndex] = useState(0);

  // Toggle function for FAQ items
  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? -1 : index);
  };

  // FAQ data
  const faqItems = [
    {
      question: "What is EventPark?",
      answer:
        "EventPark is a comprehensive event planning platform that helps you organize and manage all aspects of your events. From guest lists and RSVPs to budgeting and gift registries, we provide the tools you need to create memorable experiences.",
    },
    {
      question: "How do I create an event?",
      answer:
        "Creating an event is simple! Sign up for an account, click on 'Create Event' from your dashboard, and follow the guided steps to set up your event details, guest list, and more.",
    },
    {
      question: "Is EventPark free to use?",
      answer:
        "EventPark offers both free and premium plans. The basic features are available at no cost, while our premium plans provide additional capabilities for more complex events and professional event planners.",
    },
    {
      question: "Can I manage multiple events at once?",
      answer:
        "Absolutely! EventPark allows you to create and manage multiple events simultaneously from a single dashboard, making it perfect for event planners or individuals organizing several gatherings.",
    },
    {
      question: "How secure is my event information?",
      answer:
        "We take security seriously. All your event data and guest information is encrypted and stored securely. We never share your information with third parties without your explicit permission.",
    },
  ];

  return (
    <div
      className="w-full py-[140px] relative overflow-hidden"
      style={{
        background:
          "linear-gradient(to bottom, #FEFAF8 0%, #F5F6FE 50%, #FFFFFF 100%)",
      }}
    >
      {/* Decorative elements */}
      <div className="overflow-hidden absolute top-0 left-0 w-full h-full pointer-events-none">
        <div className="absolute -top-20 -left-20 w-40 h-40 rounded-full bg-[#FFF5F0] opacity-50"></div>
        <div className="absolute top-40 right-10 w-24 h-24 rounded-full bg-[#F5F9FF] opacity-60"></div>
        <div className="absolute bottom-20 left-1/4 w-32 h-32 rounded-full bg-[#F5F4FF] opacity-40"></div>
      </div>

      <div className="container relative z-10 px-4 mx-auto max-w-7xl">
        {/* FAQ Header */}
        <div className="mb-16 text-center mx-auto w-[662px]">
          <h2 className="font-medium text-[48px] mb-4 tracking-tight leading-[125%]">
            We answered your questions so you don’t have to ask again
          </h2>
          <p className=" text-[#0109A5] text-left mt-[60px] font-bold text-[24px] leading-[100%] tracking-[-2%]">
            What is EventPark?
          </p>
        </div>

        {/* FAQ Items */}
        <div className="p-8 mx-auto max-w-3xl bg-white rounded-2xl shadow-sm">
          {faqItems.map((item, index) => (
            <div
              key={index}
              className={`mb-6 ${
                index !== faqItems.length - 1 ? "border-b border-gray-100" : ""
              } pb-6`}
            >
              <button
                className="flex justify-between items-center w-full text-left focus:outline-none"
                onClick={() => toggleFAQ(index)}
              >
                <h3 className="text-xl font-medium text-[#333333]">
                  {item.question}
                </h3>
                <div
                  className={`w-6 h-6 flex items-center justify-center rounded-full transition-colors duration-200 ${
                    openIndex === index
                      ? "bg-[#4D55F2] text-white"
                      : "bg-[#F5F6FE]"
                  }`}
                >
                  {openIndex === index ? (
                    <svg
                      width="12"
                      height="2"
                      viewBox="0 0 12 2"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="12" height="2" rx="1" fill="currentColor" />
                    </svg>
                  ) : (
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6 1V11"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                      />
                      <path
                        d="M1 6H11"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                      />
                    </svg>
                  )}
                </div>
              </button>
              <div
                className={`mt-4 text-[#8E8E93] overflow-hidden transition-all duration-300 ${
                  openIndex === index
                    ? "max-h-96 opacity-100"
                    : "max-h-0 opacity-0"
                }`}
              >
                <p>{item.answer}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Contact CTA */}
        <div className="mt-16 text-center">
          <p className="text-[#8E8E93] mb-4">Still have questions?</p>
          <button className="bg-[#4D55F2] text-white px-6 py-3 rounded-full font-medium hover:bg-opacity-90 transition">
            Contact Us
          </button>
        </div>
      </div>
    </div>
  );
}
