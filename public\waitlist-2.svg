<svg width="400" height="600" viewBox="0 0 400 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Colorful background with gradient -->
  <rect width="400" height="600" fill="url(#paint0_linear)"/>
  
  <!-- Decorative elements -->
  <circle cx="200" cy="200" r="120" fill="#A6AAF9" fill-opacity="0.2"/>
  <circle cx="300" cy="300" r="90" fill="#FFBBA3" fill-opacity="0.3"/>
  <circle cx="100" cy="400" r="100" fill="#A6AAF9" fill-opacity="0.2"/>
  
  <!-- Couple image placeholder - in real implementation this would be an actual image -->
  <rect x="100" y="150" width="200" height="300" rx="10" fill="#4D55F2" fill-opacity="0.1"/>
  
  <!-- Decorative elements -->
  <rect x="70" y="250" width="20" height="5" rx="2.5" transform="rotate(30 70 250)" fill="#4D55F2"/>
  <rect x="180" y="120" width="20" height="5" rx="2.5" transform="rotate(-45 180 120)" fill="#FF6B3D"/>
  <rect x="320" y="380" width="20" height="5" rx="2.5" transform="rotate(75 320 380)" fill="#A6AAF9"/>
  <rect x="270" y="280" width="20" height="5" rx="2.5" transform="rotate(-25 270 280)" fill="#FFBBA3"/>
  
  <!-- Circular confetti -->
  <circle cx="100" cy="150" r="8" fill="#A6AAF9"/>
  <circle cx="240" cy="400" r="8" fill="#FFBBA3"/>
  <circle cx="350" cy="200" r="8" fill="#4D55F2"/>
  <circle cx="150" cy="350" r="8" fill="#FF6B3D"/>
  
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="400" y2="600" gradientUnits="userSpaceOnUse">
      <stop stop-color="#FEFAF8"/>
      <stop offset="1" stop-color="#F5F6FE"/>
    </linearGradient>
  </defs>
</svg>
