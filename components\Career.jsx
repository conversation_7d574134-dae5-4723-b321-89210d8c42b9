import React, { useState, useRef } from "react";
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";
// Import Swiper styles
import "swiper/css";
// Import required modules
import { Autoplay } from "swiper/modules";
// Import custom styles
import "./HeroCarousel.css";
import { Footer } from "./Footer";

// Mobile Navigation Sidebar Component (copied from FAQHero)
function MobileNavSidebar({ isOpen, onClose }) {
  const [showCompanyMenu, setShowCompanyMenu] = useState(false);

  if (!isOpen) return null;

  // Get current pathname to determine active state
  const currentPath =
    typeof window !== "undefined" ? window.location.pathname : "";

  const getActiveState = (path) => {
    if (path === "/" && currentPath === "/") return true;
    if (path === "/vendor" && currentPath === "/vendor") return true;
    if (path === "/marketplace" && currentPath === "/marketplace") return true;
    if (
      path === "company" &&
      !["/", "/vendor", "/marketplace"].includes(currentPath)
    )
      return true;
    return false;
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-40 backdrop-blur-sm bg-black/30 md:hidden"
        onClick={onClose}
      />

      {/* Sidebar */}
      <div className="fixed top-0 left-0 z-50 w-full h-full md:hidden">
        {/* Background with gradient matching the reference image */}
        <img
          src="/sidebar-illu.svg"
          alt=""
          className="top-0 translate-y-[50%] left-0 absolute"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-[#4D55F2] to-[#0109A5]">
          {/* Decorative background SVG */}

          {/* Semi-transparent white overlay with blur */}
          <div
            className="absolute inset-0"
            style={{
              background: "rgba(255, 255, 255, 0.08)",
              backdropFilter: "blur(24px)",
            }}
          />
        </div>

        {/* Content */}
        <div className="flex relative z-10 flex-col h-full">
          {/* Close button - top right */}
          <div className="flex justify-end p-6 pt-12">
            <button
              onClick={onClose}
              className="flex justify-center items-center w-9 h-9"
            >
              <svg width="36" height="36" viewBox="0 0 36 36" fill="none">
                <path
                  opacity="0.4"
                  d="M18 33C26.2843 33 33 26.2843 33 18C33 9.71573 26.2843 3 18 3C9.71573 3 3 9.71573 3 18C3 26.2843 9.71573 33 18 33Z"
                  fill="#B8BBFA"
                />
                <path
                  d="M19.5905 18L23.0405 14.55C23.4755 14.115 23.4755 13.395 23.0405 12.96C22.6055 12.525 21.8855 12.525 21.4505 12.96L18.0005 16.41L14.5505 12.96C14.1155 12.525 13.3955 12.525 12.9605 12.96C12.5255 13.395 12.5255 14.115 12.9605 14.55L16.4105 18L12.9605 21.45C12.5255 21.885 12.5255 22.605 12.9605 23.04C13.1855 23.265 13.4705 23.37 13.7555 23.37C14.0405 23.37 14.3255 23.265 14.5505 23.04L18.0005 19.59L21.4505 23.04C21.6755 23.265 21.9605 23.37 22.2455 23.37C22.5305 23.37 22.8155 23.265 23.0405 23.04C23.4755 22.605 23.4755 21.885 23.0405 21.45L19.5905 18Z"
                  fill="#B8BBFA"
                />
              </svg>
            </button>
          </div>

          {/* Logo - centered */}
          <div className="flex justify-center items-center px-6 mb-12">
            <div className="flex items-center">
              <img
                src="https://content.eventpark.africa/email/logo2.png"
                alt="EventPark"
                className="h-6"
              />
              <span className="text-white font-bold text-[24px] ml-2">
                EventPark.
              </span>
            </div>
          </div>

          {/* Navigation Items - centered */}
          <div className="flex-1 px-6">
            {!showCompanyMenu ? (
              <nav>
                <ul className="space-y-8 text-center">
                  <li>
                    <a
                      href="/"
                      className={` block py-2 px-6 rounded-full ${
                        getActiveState("/")
                          ? "font-bold bg-[white]/8 backdrop-blur-md text-[#B8BBFA] text-[40px]"
                          : "text-[24px] font-normal text-[#B8BBFA]"
                      }`}
                    >
                      Home
                    </a>
                  </li>
                  <li>
                    <button
                      onClick={() => setShowCompanyMenu(true)}
                      className={` block py-2 px-6 rounded-full w-full ${
                        getActiveState("company")
                          ? "font-bold text-[40px] bg-[white]/8 backdrop-blur-md text-[#B8BBFA]"
                          : "font-normal text-[24px] text-[#B8BBFA]"
                      }`}
                    >
                      Company
                    </button>
                  </li>
                  <li>
                    <a
                      href="/vendor"
                      className={` block py-2 px-6 rounded-full ${
                        getActiveState("/vendor")
                          ? "font-bold bg-[white]/8 backdrop-blur-md text-[#B8BBFA] text-[40px]"
                          : "text-[24px] font-normal text-[#B8BBFA]"
                      }`}
                    >
                      For Vendors
                    </a>
                  </li>
                  <li>
                    <a
                      href="/marketplace"
                      className={` block py-2 px-6 rounded-full ${
                        getActiveState("/marketplace")
                          ? "font-bold bg-[white]/8 backdrop-blur-md text-[#B8BBFA] text-[40px]"
                          : "text-[24px] font-normal text-[#B8BBFA]"
                      }`}
                    >
                      Marketplace
                    </a>
                  </li>
                </ul>
              </nav>
            ) : (
              /* Company Submenu */
              <div className="text-center bg-[white]/8 backdrop-blur-md my-6 rounded-md">
                <div className="mb-8">
                  <button
                    onClick={() => setShowCompanyMenu(false)}
                    className="text-white text-[40px] font-bold block py-2 px-6  text-[#B8BBFA] w-full"
                  >
                    Company
                  </button>
                </div>
                <nav>
                  <ul className="space-y-6">
                    <li>
                      <a
                        href="/about"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        About Us
                      </a>
                    </li>
                    <li>
                      <a
                        href="/contact"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Contact Us
                      </a>
                    </li>
                    <li>
                      <a
                        href="/privacy-policy"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Privacy Policy
                      </a>
                    </li>
                    <li>
                      <a
                        href="/terms-of-service"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Terms of Use
                      </a>
                    </li>
                    <li>
                      <a
                        href="/career"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Careers
                      </a>
                    </li>
                    <li>
                      <a
                        href="/blog"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Blog
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            )}
          </div>

          {/* Account Buttons */}
          <div className="px-6 pb-12 space-y-4">
            <button className="w-full py-4 px-6 bg-[#9CC1FC] text-black rounded-full font-medium text-[18px]">
              Create an Account
            </button>
            <button className="w-full py-4 px-6 bg-[#4D55F2] text-white rounded-full font-medium text-[18px]">
              Sign in to your account
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

export function Career() {
  const [activeTab, setActiveTab] = useState("all");
  const [swiperInitialized, setSwiperInitialized] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const swiperRef = useRef(null);

  // Job categories
  const categories = [
    { id: "all", name: "All Jobs" },
    { id: "engineering", name: "Engineering" },
    { id: "design", name: "Design" },
    { id: "marketing", name: "Marketing" },
    { id: "product", name: "Product" },
  ];

  // Team members for carousel
  const teamMembers = [
    { id: 1, image: "/career1.svg", alt: "Team member 1" },
    { id: 2, image: "/career2.svg", alt: "Team member 2" },
    { id: 3, image: "/career3.svg", alt: "Team member 3" },
    { id: 4, image: "/career4.svg", alt: "Team member 4" },
    { id: 5, image: "/career5.svg", alt: "Team member 5" },
  ];

  return (
    <div className="w-full">
      {/* Mobile Navigation Sidebar */}
      <MobileNavSidebar
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />

      {/* Hero Section */}
      <div
        className="relative w-full  h-[850px] h-full pb-20 pt-16 flex flex-col items-center justify-start "
        style={{
          backgroundImage:
            "linear-gradient(rgb(235, 243, 254, 40%)), url('/career-bg.jpg')",
          backgroundSize: "cover",
          backgroundBlendMode: "normal",
          backgroundPosition: "center",
        }}
      >
        <div className="container py-8 mx-auto max-w-7xl">
          <div className="flex flex-col items-center w-full text-center">
            <div className="container flex flex-col justify-center items-center px-4 mb-8">
              {/* Header with logo and mobile controls - same as FAQHero */}
              <div className="flex relative justify-center items-center mb-6 w-full">
                {/* Logo - Center */}
                <div className="flex items-center">
                  <img src="/logo.svg" alt="EventPark" className="h-6" />
                  <span className="text-[#4D55F2] font-bold text-[24px] ml-2">
                    EventPark.
                  </span>
                </div>

                {/* Mobile Hamburger Menu - Right side */}
                <button
                  onClick={() => setIsMobileMenuOpen(true)}
                  className="flex absolute right-0 justify-center items-center w-10 h-10 md:hidden"
                >
                  <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                    <circle
                      cx="20"
                      cy="20"
                      r="20"
                      fill="#FEF5F1"
                      fillOpacity="0.8"
                    />
                    <rect
                      x="16.5"
                      y="24.8999"
                      width="11.6667"
                      height="1"
                      fill="#7D665C"
                    />
                    <rect
                      x="11.8335"
                      y="19.0667"
                      width="16.3333"
                      height="1"
                      fill="#7D665C"
                    />
                    <rect
                      x="16.5"
                      y="13.2334"
                      width="11.6667"
                      height="1"
                      fill="#7D665C"
                    />
                  </svg>
                </button>
              </div>
            </div>
            {/* Hero Content */}
            <div className="container mx-auto">
              <p className="text-[#8E8E93] uppercase tracking-wider mt-10 text-[18px] ">
                CAREER
              </p>
              <h1 className="flex flex-row items-center justify-center gap-2 text-[48px] md:text-[120px] font-semibold mb-2">
                <span className="text-black">Join the</span>
                <span className="text-[#4D55F2]">Team</span>
              </h1>

              <p className="text-[#8E8E93] text-[24px] md:max-w-[500px] mb-6 max-w-[900%] mx-auto">
                Be a part of the movement innovating and transforming event
                planning
              </p>

              {/* CTA Button */}
              <div className="flex justify-center mb-8">
                <a href="#viewopening">
                  <button className="bg-[#9CC1FC] flex justify-center items-center text-black text-sm font-semibold py-2 px-4 rounded-[50px] shadow-md">
                    View Opening
                  </button>
                </a>
              </div>

              {/* Team Members Carousel */}
              <div className=" mb-[80px] container mx-auto mt-12 relative flex justify-center items-center">
                <div
                  className="overflow-hidden mx-auto md:w-[750px]  hero-swiper-container2"
                  // style={{ maxWidth: "1440px", margin: "0 auto" }}
                >
                  <Swiper
                    slidesPerView={4.3}
                    spaceBetween={20}
                    centeredSlides={true}
                    loop={true}
                    speed={800}
                    watchSlidesProgress={true}
                    grabCursor={true}
                    autoplay={{
                      delay: 3000,
                      disableOnInteraction: false,
                      pauseOnMouseEnter: true,
                    }}
                    breakpoints={{
                      320: {
                        slidesPerView: 3,
                        spaceBetween: 20,
                      },
                      640: {
                        slidesPerView: 5,
                        spaceBetween: 10,
                      },
                      1024: {
                        slidesPerView: 3,
                        spaceBetween: 10,
                      },
                      1440: {
                        slidesPerView: 4,
                        spaceBetween: 10,
                      },
                    }}
                    modules={[Autoplay]}
                    className="mySwiper"
                    onSwiper={(swiper) => {
                      swiperRef.current = swiper;
                      setSwiperInitialized(true);
                    }}
                  >
                    {/* Add extra slides for smoother looping */}
                    {[...teamMembers, ...teamMembers, ...teamMembers].map(
                      (member, index) => (
                        <SwiperSlide key={index}>
                          <div className="flex justify-center items-center">
                            {swiperInitialized ? (
                              <img
                                src={member.image}
                                loading="lazy"
                                alt={member.alt}
                                className="md:min-w-[200px] max-w-[200px] max-h-[200px] min-w-[100px] min-h-[100px] md:min-h-[200px] object-cover rounded-3xl shadow-md"
                              />
                            ) : (
                              <div className="w-[200px] h-[200px] bg-gray-200 rounded-3xl animate-pulse"></div>
                            )}
                          </div>
                        </SwiperSlide>
                      )
                    )}
                  </Swiper>
                </div>
              </div>

              <nav className="flex justify-center">
                <ul className="flex px-8 py-3 space-x-12 bg-white rounded-full shadow-lg">
                  <li>
                    <a
                      href="/"
                      className="text-[10px] sm:text-[12px] md:text-sm font-medium text-black hover:text-[#4D55F2] relative group"
                    >
                      HOME
                      <span className="absolute bottom-[-8px] left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-transparent group-hover:bg-[#FF5519]"></span>
                    </a>
                  </li>
                  {/* <li>
                    <a
                      href="/c"
                      className="text-sm font-medium text-[#4D55F2] relative group"
                    >
                      COMPANY
                      <span className="absolute bottom-[-8px] left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-[#FF5519]"></span>
                    </a>
                  </li> */}
                  <li>
                    <a
                      href="/vendor"
                      className="text-[10px] sm:text-[12px] md:text-sm font-medium text-black hover:text-[#4D55F2] relative group"
                    >
                      FOR VENDORS
                      <span className="absolute bottom-[-8px] left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-transparent group-hover:bg-[#FF5519]"></span>
                    </a>
                  </li>
                  <li>
                    <a
                      href="/marketplace"
                      className="text-[10px] sm:text-[12px] md:text-sm font-medium text-black hover:text-[#4D55F2] relative group"
                    >
                      MARKETPLACE
                      <span className="absolute bottom-[-8px] left-1/2 transform -translate-x-1/2 w-1 h-1 rounded-full bg-transparent group-hover:bg-[#FF5519]"></span>
                    </a>
                  </li>
                </ul>
              </nav>

              {/* Job Categories */}
              {/* <div className="flex flex-wrap gap-4 justify-center mb-8 text-sm">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveTab(category.id)}
                    className={`px-4 py-1 rounded-full transition-all duration-200 ${
                      activeTab === category.id
                        ? "bg-[#4D55F2] text-white"
                        : "bg-white text-[#666666] border border-gray-200"
                    }`}
                  >
                    {category.name}
                  </button>
                ))}
              </div> */}
            </div>
          </div>
        </div>
      </div>

      {/* Mission Statement Section */}
      <div className="py-16 bg-white">
        <div className="container xl:max-w-[1120px] mx-auto max-w-5xl">
          {/* Mission Statement */}
          <div className="mb-16 text-center">
            <h2 className="mb-6 text-lg md:text-2xl font-medium text-[#8E8E93] tracking-wide uppercase">
              MISSION STATEMENT
            </h2>
            <p className="mx-auto md:max-w-[750px] text-[20px] md:text-[28px] leading-[180%] text-center text-black">
              At EventPark, we simplify event planning by connecting people,
              fostering collaboration, and delivering exceptional experiences.
              Join us to drive innovation and creativity!
            </p>
          </div>

          {/* Company Values Grid */}
          <div className="grid grid-cols-1 gap-6 px-4 sm:grid-cols-2 lg:grid-cols-3">
            {/* Innovation */}
            <div className="p-8 bg-[#E1ECFE] rounded-2xl h-[280px] flex flex-col justify-between ">
              <div className="flex justify-center items-center mb-4 w-12 h-12 bg-[#CDE0FD] rounded-[50%] shadow-sm">
                <svg
                  width="24"
                  height="25"
                  viewBox="0 0 24 25"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M12.0007 4.83317C15.8666 4.83317 19.0007 7.96718 19.0007 11.8332C19.0007 14.4241 17.593 16.6863 15.5007 17.8967L15.5007 22.3332L14.334 23.4998H13.1673C13.1673 24.1442 12.645 24.6665 12.0007 24.6665C11.3563 24.6665 10.834 24.1442 10.834 23.4998H9.66732L8.50065 22.3332L8.50063 17.8967C6.40834 16.6863 5.00065 14.4242 5.00065 11.8332C5.00065 7.96718 8.13466 4.83317 12.0007 4.83317ZM13.1674 18.7364C12.7879 18.8 12.3982 18.8332 12.0007 18.8332C11.6031 18.8332 11.2134 18.8 10.8339 18.7364L10.834 21.1665H13.1673L13.1674 18.7364ZM5.60723 16.9892L6.84466 18.2266L4.36979 20.7015L3.13235 19.464L5.60723 16.9892ZM18.3941 16.9892L20.8689 19.464L19.6315 20.7015L17.1566 18.2266L18.3941 16.9892ZM23.6673 10.9582V12.7082H20.1673V10.9582H23.6673ZM3.83398 10.9582V12.7082H0.333984V10.9582H3.83398ZM19.6315 2.96487L20.8689 4.20231L18.3941 6.67718L17.1566 5.43975L19.6315 2.96487ZM4.36979 2.96487L6.84466 5.43975L5.60723 6.67718L3.13235 4.20231L4.36979 2.96487ZM12.8757 0.166504V3.6665H11.1257V0.166504H12.8757Z"
                    fill="#5075AF"
                  />
                </svg>
              </div>
              <div className="self-end">
                <h3 className="mb-3 text-[28px] leading-[130%] font-semibold text-[black]">
                  Innovation
                </h3>
                <p className="text-sm text-[#8E8E93] font-medium">
                  We push boundaries to create better solutions every day.
                </p>
              </div>
            </div>

            {/* Collaboration */}
            <div className="p-8 bg-[#F5F9FF] rounded-2xl h-[280px] flex flex-col justify-between">
              <div className="flex justify-center items-center mb-4 w-12 h-12 rounded-lg rounded-[50%] shadow-sm bg-black/3">
                <svg
                  width="28"
                  height="28"
                  viewBox="0 0 28 28"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.5007 2.3335C7.44398 2.3335 4.95898 4.8185 4.95898 7.87516C4.95898 10.8735 7.30398 13.3002 10.3607 13.4052C10.454 13.3935 10.5473 13.3935 10.6173 13.4052C10.6407 13.4052 10.6523 13.4052 10.6756 13.4052C10.6873 13.4052 10.6873 13.4052 10.699 13.4052C13.6856 13.3002 16.0306 10.8735 16.0423 7.87516C16.0423 4.8185 13.5573 2.3335 10.5007 2.3335Z"
                    fill="#4D55F2"
                  />
                  <path
                    d="M16.4271 16.5084C13.1721 14.3384 7.86378 14.3384 4.58544 16.5084C3.10378 17.5 2.28711 18.8417 2.28711 20.2767C2.28711 21.7117 3.10378 23.0417 4.57378 24.0217C6.20711 25.1184 8.35378 25.6667 10.5004 25.6667C12.6471 25.6667 14.7938 25.1184 16.4271 24.0217C17.8971 23.03 18.7138 21.7 18.7138 20.2534C18.7021 18.8184 17.8971 17.4884 16.4271 16.5084Z"
                    fill="#4D55F2"
                  />
                  <path
                    d="M23.3229 8.56361C23.5096 10.8269 21.8996 12.8103 19.6712 13.0786C19.6596 13.0786 19.6596 13.0786 19.6479 13.0786H19.6129C19.5429 13.0786 19.4729 13.0786 19.4146 13.1019C18.2829 13.1603 17.2446 12.7986 16.4629 12.1336C17.6646 11.0603 18.3529 9.45028 18.2129 7.70028C18.1312 6.75528 17.8046 5.89195 17.3146 5.15695C17.7579 4.93528 18.2712 4.79528 18.7962 4.74861C21.0829 4.55028 23.1246 6.25361 23.3229 8.56361Z"
                    fill="#4D55F2"
                  />
                  <path
                    d="M25.6556 19.3551C25.5623 20.4868 24.8389 21.4668 23.6256 22.1318C22.4589 22.7735 20.9889 23.0768 19.5306 23.0418C20.3706 22.2835 20.8606 21.3385 20.9539 20.3351C21.0706 18.8885 20.3823 17.5001 19.0056 16.3918C18.2239 15.7735 17.3139 15.2835 16.3223 14.9218C18.9006 14.1751 22.1439 14.6768 24.1389 16.2868C25.2123 17.1501 25.7606 18.2351 25.6556 19.3551Z"
                    fill="#4D55F2"
                  />
                </svg>
              </div>
              <div>
                <h3 className="mb-3 text-[28px] leading-[130%] font-semibold text-[black]">
                  Collaboration
                </h3>
                <p className="text-sm text-[#8E8E93] font-medium">
                  We achieve more together through shared purpose.
                </p>
              </div>
            </div>

            {/* Customer-Centricity */}
            <div className="p-8 bg-[#FEFAF8] flex flex-col justify-between rounded-2xl">
              <div className="flex justify-center items-center mb-4 w-12 h-12 bg-[black]/3  rounded-[50%] shadow-sm">
                <svg
                  width="28"
                  height="28"
                  viewBox="0 0 28 28"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M19.1807 3.6167C17.069 3.6167 15.179 4.64337 14.0007 6.21837C12.8223 4.64337 10.9323 3.6167 8.82065 3.6167C5.23898 3.6167 2.33398 6.53337 2.33398 10.1384C2.33398 11.5267 2.55565 12.81 2.94065 14C4.78398 19.8334 10.4657 23.3217 13.2773 24.2784C13.674 24.4184 14.3273 24.4184 14.724 24.2784C17.5357 23.3217 23.2173 19.8334 25.0606 14C25.4457 12.81 25.6673 11.5267 25.6673 10.1384C25.6673 6.53337 22.7623 3.6167 19.1807 3.6167Z"
                    fill="#C9B2A8"
                  />
                </svg>
              </div>
              <h3 className="mb-3 text-[28px] leading-[130%] min-w-fit font-semibold text-[black]">
                Customer-Centricity
              </h3>
              <p className="text-sm text-[#8E8E93] font-medium">
                Everything we do is centered around you.
              </p>
            </div>

            {/* Integrity */}
            <div className="p-8 bg-[#F5F9FF] rounded-2xl h-[280px] flex flex-col justify-between">
              <div className="flex justify-center items-center mb-4 w-12 h-12 bg-[black]/3  rounded-[50%] shadow-sm">
                <svg
                  width="28"
                  height="28"
                  viewBox="0 0 28 28"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M22.7501 8.75019L21.0117 10.4885L17.5117 6.98852L19.2501 5.25019C19.7401 4.76019 20.3701 4.52686 21.0001 4.52686C21.6301 4.52686 22.2601 4.76019 22.7501 5.25019C23.7184 6.21852 23.7184 7.78186 22.7501 8.75019Z"
                    fill="#4D55F2"
                  />
                  <path
                    d="M20.1953 11.3169L7.58367 23.9169C6.61534 24.8852 5.05201 24.8852 4.08367 23.9169C3.11534 22.9486 3.11534 21.3852 4.08367 20.4169L16.6953 7.81689L20.1953 11.3169Z"
                    fill="#4D55F2"
                  />
                  <path
                    d="M11.6083 4.0836L12.0866 2.46193C12.1333 2.31026 12.0866 2.14693 11.9816 2.03026C11.8766 1.9136 11.6899 1.86693 11.5383 1.9136L9.9166 2.39193L8.29494 1.9136C8.14327 1.86693 7.97994 1.9136 7.86327 2.0186C7.7466 2.13526 7.71161 2.2986 7.75827 2.45026L8.22494 4.0836L7.7466 5.70526C7.69994 5.85693 7.74661 6.02026 7.85161 6.13693C7.96827 6.2536 8.13161 6.2886 8.28327 6.24193L9.9166 5.77526L11.5383 6.2536C11.5849 6.26526 11.6199 6.27693 11.6666 6.27693C11.7833 6.27693 11.8883 6.23026 11.9816 6.1486C12.0983 6.03193 12.1333 5.8686 12.0866 5.71693L11.6083 4.0836Z"
                    fill="#4D55F2"
                  />
                  <path
                    d="M6.9403 11.0836L7.41864 9.46193C7.4653 9.31026 7.41864 9.14693 7.31364 9.03026C7.19697 8.9136 7.03364 8.8786 6.88197 8.92526L5.24864 9.39193L3.62697 8.9136C3.4753 8.86693 3.31197 8.9136 3.1953 9.0186C3.07864 9.13526 3.04364 9.2986 3.0903 9.45026L3.55697 11.0836L3.07864 12.7053C3.03197 12.8569 3.07864 13.0203 3.18364 13.1369C3.3003 13.2536 3.46364 13.2886 3.6153 13.2419L5.23697 12.7636L6.85864 13.2419C6.89364 13.2536 6.9403 13.2536 6.98697 13.2536C7.10364 13.2536 7.20864 13.2069 7.30197 13.1253C7.41864 13.0086 7.45364 12.8453 7.40697 12.6936L6.9403 11.0836Z"
                    fill="#4D55F2"
                  />
                  <path
                    d="M24.4404 16.9166L24.9187 15.2949C24.9654 15.1433 24.9187 14.9799 24.8137 14.8633C24.697 14.7466 24.5337 14.7116 24.382 14.7583L22.7604 15.2366L21.1387 14.7583C20.987 14.7116 20.8237 14.7583 20.707 14.8633C20.5904 14.9799 20.5554 15.1433 20.602 15.2949L21.0804 16.9166L20.602 18.5383C20.5554 18.6899 20.602 18.8533 20.707 18.9699C20.8237 19.0866 20.987 19.1216 21.1387 19.0749L22.7604 18.5966L24.382 19.0749C24.417 19.0866 24.4637 19.0866 24.5104 19.0866C24.627 19.0866 24.732 19.0399 24.8254 18.9583C24.942 18.8416 24.977 18.6783 24.9304 18.5266L24.4404 16.9166Z"
                    fill="#4D55F2"
                  />
                </svg>
              </div>
              <div>
                <h3 className="mb-3 text-[28px] leading-[130%] font-semibold text-[black]">
                  Integrity
                </h3>
                <p className="text-sm text-[#8E8E93] font-medium">
                  We uphold ethical standards, transparency, and accountability.
                </p>
              </div>
            </div>

            {/* Excellence */}
            <div className="p-8 bg-[#F5F9FF] rounded-2xl h-[280px] flex flex-col justify-between">
              <div className="flex justify-center items-center mb-4 w-12 h-12 bg-[black]/3  rounded-[50%] shadow-sm">
                <svg
                  width="28"
                  height="28"
                  viewBox="0 0 28 28"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17.9563 6.078L19.6013 9.368C19.823 9.823 20.418 10.2547 20.9197 10.348L23.8946 10.838C25.7963 11.153 26.2397 12.5297 24.8746 13.9063L22.553 16.228C22.168 16.613 21.9463 17.3713 22.0746 17.9197L22.7396 20.7897C23.2647 23.053 22.0513 23.9397 20.0563 22.7497L17.268 21.093C16.7663 20.7897 15.9263 20.7897 15.4247 21.093L12.6363 22.7497C10.6413 23.928 9.42798 23.053 9.95298 20.7897L10.618 17.9197C10.723 17.3597 10.5013 16.6013 10.1163 16.2163L7.79465 13.8947C6.42965 12.5297 6.87298 11.153 8.77465 10.8263L11.7496 10.3363C12.2513 10.2547 12.8463 9.81134 13.068 9.35634L14.713 6.06634C15.6113 4.293 17.058 4.29301 17.9563 6.078Z"
                    fill="#967F75"
                  />
                  <path
                    d="M9.33398 6.7085H2.33398C1.85565 6.7085 1.45898 6.31183 1.45898 5.8335C1.45898 5.35516 1.85565 4.9585 2.33398 4.9585H9.33398C9.81232 4.9585 10.209 5.35516 10.209 5.8335C10.209 6.31183 9.81232 6.7085 9.33398 6.7085Z"
                    fill="#967F75"
                  />
                  <path
                    d="M5.83398 23.0415H2.33398C1.85565 23.0415 1.45898 22.6448 1.45898 22.1665C1.45898 21.6882 1.85565 21.2915 2.33398 21.2915H5.83398C6.31232 21.2915 6.70898 21.6882 6.70898 22.1665C6.70898 22.6448 6.31232 23.0415 5.83398 23.0415Z"
                    fill="#967F75"
                  />
                  <path
                    d="M3.50065 14.875H2.33398C1.85565 14.875 1.45898 14.4783 1.45898 14C1.45898 13.5217 1.85565 13.125 2.33398 13.125H3.50065C3.97898 13.125 4.37565 13.5217 4.37565 14C4.37565 14.4783 3.97898 14.875 3.50065 14.875Z"
                    fill="#967F75"
                  />
                </svg>
              </div>
              <div>
                <h3 className="mb-3 text-[28px] leading-[130%] font-semibold text-[black]">
                  Excellence
                </h3>
                <p className="text-sm text-[#8E8E93] font-medium">
                  We pursue the highest standards in all we do.
                </p>
              </div>
            </div>

            {/* Inclusivity */}
            <div className="p-8 bg-[#F5F9FF] rounded-2xl flex flex-col justify-between h-[280px]">
              <div className="flex justify-center items-center mb-4 w-12 h-12 bg-[black]/3  rounded-[50%] shadow-sm">
                <svg
                  width="28"
                  height="28"
                  viewBox="0 0 28 28"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10.4994 16.5898H4.57269C3.86102 16.5898 3.20769 16.9515 2.83436 17.5582C2.46102 18.1532 2.42602 18.8648 2.72936 19.4948C4.16436 22.4348 6.75436 24.7448 9.83436 25.8298C10.0444 25.8998 10.2777 25.9465 10.4994 25.9465C10.9077 25.9465 11.316 25.8182 11.666 25.5732C12.2144 25.1882 12.541 24.5582 12.541 23.8932L12.5527 18.6432C12.5527 18.0948 12.3427 17.5815 11.9577 17.1965C11.561 16.8115 11.0477 16.5898 10.4994 16.5898Z"
                    fill="#5075AF"
                  />
                  <path
                    d="M26.2269 11.2002C24.9202 5.46016 19.8919 1.4585 14.0002 1.4585C8.10857 1.4585 3.08024 5.46016 1.77357 11.2002C1.63357 11.8068 1.77357 12.4252 2.17024 12.9152C2.56691 13.4052 3.15024 13.6852 3.78024 13.6852H24.2319C24.8619 13.6852 25.4452 13.4052 25.8419 12.9152C26.2269 12.4252 26.3669 11.7952 26.2269 11.2002Z"
                    fill="#5075AF"
                  />
                  <path
                    d="M23.404 16.6484L17.5007 16.6367C16.9523 16.6367 16.439 16.8467 16.054 17.2317C15.669 17.6167 15.459 18.1301 15.459 18.6784L15.4707 23.9051C15.4707 24.5701 15.7973 25.2001 16.3457 25.5851C16.6957 25.8301 17.104 25.9584 17.5123 25.9584C17.734 25.9584 17.9557 25.9234 18.1657 25.8417C21.2223 24.7684 23.8123 22.4701 25.2473 19.5651C25.5506 18.9467 25.5156 18.2234 25.154 17.6401C24.769 17.0101 24.1157 16.6484 23.404 16.6484Z"
                    fill="#5075AF"
                  />
                </svg>
              </div>
              <div>
                <h3 className="mb-3 text-[28px] leading-[130%] font-semibold text-[black]">
                  Inclusivity
                </h3>
                <p className="text-sm text-[#8E8E93] font-medium">
                  We create a platform where everyone belongs.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* EventPark Values Section */}
      <div className="py-16 bg-transparent md:bg-gradient-to-b from-[#FEF7F4] to-white">
        <div className="container xl:max-w-[1120px] mx-auto max-w-5xl px-4">
          <div className="mb-12 text-center">
            <p className="text-[#8E8E93] font-medium text-[24px] sm:text-[32px] md:text-[56px] max-w-[700px] mx-auto">
              Why work with us at
            </p>
            <h2 className="text-[#000000] text-[24px] sm:text-[36px] md:text-[56px] font-semibold mb-4">
              Eventpark?
            </h2>
          </div>

          {/* Three Box Grid */}
          <div className="flex overflow-x-auto gap-8 pb-4 scrollbar-hide md:grid md:grid-cols-3 md:overflow-visible">
            {/* Make an Impact Box */}
            <div className="bg-[#FEF7F4] relative rounded-2xl py-8 flex flex-col justify-between min-h-[490px] w-[330px] md:w-auto min-w-[280px] md:min-w-0 flex-shrink-0 relative">
              <svg
                className="absolute top-0 left-0"
                width="226"
                height="250"
                viewBox="0 0 226 250"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.4"
                  d="M177.254 36.2498C177.254 42.9998 173.629 49.1244 167.879 52.2494L146.129 63.9994L127.629 73.8749L89.254 94.6246C85.129 96.8746 80.629 97.9998 76.004 97.9998C71.379 97.9998 66.879 96.8746 62.754 94.6246L-15.8711 52.2494C-21.6211 49.1244 -25.2461 42.9998 -25.2461 36.2498C-25.2461 29.4998 -21.6211 23.3744 -15.8711 20.2494L8.75392 6.99945L28.379 -3.62525L62.754 -22.125C71.004 -26.625 81.004 -26.625 89.254 -22.125L167.879 20.2494C173.629 23.3744 177.254 29.4998 177.254 36.2498Z"
                  fill="#FCE5DB"
                />
                <path
                  opacity="0.4"
                  d="M49.746 109.375L-23.3791 72.7503C-29.0041 69.8753 -35.504 70.2503 -40.879 73.5003C-46.254 76.7503 -49.3789 82.5007 -49.3789 88.7507V157.875C-49.3789 169.875 -42.7541 180.625 -32.0041 186L41.121 222.501C43.621 223.751 46.3712 224.375 49.1212 224.375C52.3712 224.375 55.6212 223.501 58.4962 221.626C63.8712 218.376 66.9961 212.625 66.9961 206.375V137.251C67.1211 125.501 60.496 114.75 49.746 109.375Z"
                  fill="#FCE5DB"
                />
                <path
                  opacity="0.4"
                  d="M201.375 88.8752V158C201.375 169.875 194.75 180.625 184 186L110.875 222.625C108.375 223.875 105.625 224.5 102.875 224.5C99.625 224.5 96.3753 223.625 93.3753 221.75C88.1253 218.5 84.875 212.75 84.875 206.5V137.5C84.875 125.5 91.5002 114.75 102.25 109.375L129.125 95.9996L147.875 86.6246L175.375 72.8749C181 69.9999 187.5 70.2498 192.875 73.6248C198.125 76.8748 201.375 82.6252 201.375 88.8752Z"
                  fill="#FCE5DB"
                />
                <path
                  d="M146.125 63.9997L127.625 73.8752L8.75 6.99969L28.3751 -3.625L143.125 61.1249C144.375 61.8749 145.375 62.8747 146.125 63.9997Z"
                  fill="#FCE5DB"
                />
                <path
                  d="M147.875 86.6245V115C147.875 120.125 143.625 124.375 138.5 124.375C133.375 124.375 129.125 120.125 129.125 115V95.9995L147.875 86.6245Z"
                  fill="#FCE5DB"
                />
              </svg>
              <div></div>
              <div className="px-8 mb-6">
                <h3 className="text-[28px] font-semibold text-black mb-4">
                  Make an Impact
                </h3>
                <p className="text-[#666666] text-base">
                  Join a team transforming event planning by connecting
                  organizers and vendors accross africa
                </p>
              </div>
            </div>

            {/* Grow and Thrive Box */}
            <div className="bg-[#1A22BF] relative rounded-2xl py-8 flex flex-col justify-between min-h-[490px] w-[330px] md:w-auto min-w-[280px] md:min-w-0 flex-shrink-0">
              <svg
                className="absolute top-0 right-0"
                width="222"
                height="238"
                viewBox="0 0 222 238"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M219.166 125.833L209.583 116.458L128.854 197.084L138.541 206.771C146.666 214.896 155.104 218.959 163.437 218.959C171.77 218.959 180.208 214.896 188.333 206.771L219.166 175.938C235.729 159.271 235.729 142.5 219.166 125.833Z"
                  fill="url(#paint0_linear_10426_15796)"
                  fill-opacity="0.16"
                />
                <path
                  d="M111.355 18.021C95.0006 1.66683 77.8131 1.66683 61.459 18.021L30.5215 48.8543C14.2715 65.2085 14.2715 82.396 30.5215 98.7502L40.1048 108.334L120.834 27.6043L111.355 18.021Z"
                  fill="url(#paint1_linear_10426_15796)"
                  fill-opacity="0.16"
                />
                <path
                  opacity="0.4"
                  d="M227.188 28.5415C213.542 62.604 182.396 107.083 152.709 136.146C148.438 109.271 126.98 88.229 99.8965 84.479C129.063 54.6873 173.855 23.1248 208.021 9.37479C214.063 7.08313 220.105 8.85396 223.855 12.604C227.813 16.5623 229.688 22.4998 227.188 28.5415Z"
                  fill="url(#paint2_linear_10426_15796)"
                  fill-opacity="0.16"
                />
                <path
                  d="M152.704 136.147C147.496 141.251 142.288 145.938 137.288 149.897L116.767 166.355C114.163 168.23 111.559 169.792 108.642 171.042C108.642 169.063 108.434 167.084 108.225 165.001C107.079 156.355 103.121 148.126 96.1419 141.147C88.9544 134.063 80.4128 130.001 71.5586 128.855C69.4753 128.647 67.2878 128.542 65.3086 128.647C66.4544 125.417 68.2253 122.397 70.4128 119.897L86.6628 99.3758C90.5169 94.48 94.9961 89.48 99.8919 84.48C126.975 88.23 148.434 109.272 152.704 136.147Z"
                  fill="url(#paint3_linear_10426_15796)"
                  fill-opacity="0.16"
                />
                <path
                  opacity="0.4"
                  d="M108.646 171.042C108.646 182.5 104.271 193.438 95.9377 201.667C89.5835 208.125 81.0418 212.5 70.6252 213.75L45.1043 216.563C31.146 218.125 19.1668 206.146 20.7293 192.083L23.5418 166.458C26.0418 143.646 45.1043 129.063 65.3127 128.646C67.2918 128.542 69.4793 128.646 71.5627 128.854C80.4168 130 88.9585 134.063 96.146 141.146C103.125 148.125 107.083 156.354 108.229 165C108.438 167.083 108.646 169.063 108.646 171.042Z"
                  fill="url(#paint4_linear_10426_15796)"
                  fill-opacity="0.16"
                />
                <defs>
                  <linearGradient
                    id="paint0_linear_10426_15796"
                    x1="180.221"
                    y1="116.458"
                    x2="180.221"
                    y2="218.958"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop offset="0.425" stop-color="#F2F2F2" />
                    <stop offset="1" stop-color="#D1D1D1" />
                  </linearGradient>
                  <linearGradient
                    id="paint1_linear_10426_15796"
                    x1="69.584"
                    y1="5.75537"
                    x2="69.584"
                    y2="108.334"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop offset="0.425" stop-color="#F2F2F2" />
                    <stop offset="1" stop-color="#D1D1D1" />
                  </linearGradient>
                  <linearGradient
                    id="paint2_linear_10426_15796"
                    x1="164.108"
                    y1="8.35254"
                    x2="164.108"
                    y2="136.146"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop offset="0.425" stop-color="#F2F2F2" />
                    <stop offset="1" stop-color="#D1D1D1" />
                  </linearGradient>
                  <linearGradient
                    id="paint3_linear_10426_15796"
                    x1="109.007"
                    y1="84.48"
                    x2="109.007"
                    y2="171.042"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop offset="0.425" stop-color="#F2F2F2" />
                    <stop offset="1" stop-color="#D1D1D1" />
                  </linearGradient>
                  <linearGradient
                    id="paint4_linear_10426_15796"
                    x1="64.6189"
                    y1="128.605"
                    x2="64.6189"
                    y2="216.7"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop offset="0.425" stop-color="#F2F2F2" />
                    <stop offset="1" stop-color="#D1D1D1" />
                  </linearGradient>
                </defs>
              </svg>
              <div></div>
              <div className="px-8 mb-6">
                <h3 className="text-[28px] font-semibold text-white mb-4">
                  Grow and Thrive
                </h3>
                <p className="text-base text-white">
                  We foster a culture of continuous learning and development.
                  Expand your skills, take on new challenges, and advance your
                  career with us.
                </p>
              </div>
            </div>

            {/* The Power of Innovation Box */}
            <div className="bg-[#FEF7F4] relative rounded-2xl py-8 flex flex-col justify-between min-h-[490px] w-[330px] md:w-auto min-w-[280px] md:min-w-0 flex-shrink-0">
              <svg
                className="absolute top-0 right-0"
                width="195"
                height="257"
                viewBox="0 0 195 257"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M119.526 44.3066C160.924 40.6475 197.45 71.241 201.109 112.639C203.562 140.384 190.629 165.94 169.37 180.881L173.568 228.388L162.18 241.986L149.687 243.09C150.297 249.989 145.198 256.077 138.298 256.687C131.398 257.297 125.311 252.198 124.701 245.298L112.208 246.402L98.6106 235.014L94.4114 187.507C70.861 176.526 53.646 153.635 51.1937 125.89C47.5347 84.4916 78.1281 47.9657 119.526 44.3066ZM145.179 192.081C141.176 193.122 137.034 193.846 132.777 194.222C128.52 194.598 124.315 194.612 120.192 194.29L122.492 220.312L147.478 218.104L145.179 192.081ZM62.5691 180.527L76.9911 192.607L52.832 221.451L38.41 209.371L62.5691 180.527ZM199.494 168.425L228.338 192.584L216.258 207.006L187.414 182.847L199.494 168.425ZM250.253 98.8524L251.909 117.592L214.43 120.905L212.774 102.165L250.253 98.8524ZM37.8726 117.624L39.5289 136.364L2.05006 139.676L0.393732 120.937L37.8726 117.624ZM199.471 17.078L213.893 29.1575L189.734 58.0015L175.312 45.9219L199.471 17.078ZM36.0447 31.5228L64.8887 55.682L52.8091 70.104L23.9651 45.9448L36.0447 31.5228ZM124.479 -6.49342L127.792 30.9855L109.052 32.6418L105.74 -4.83709L124.479 -6.49342Z"
                  fill="#FCE5DB"
                />
              </svg>
              <div></div>
              <div className="px-8 mb-6">
                <h3 className="text-[28px] font-semibold text-black mb-4">
                  The Power of Innovation
                </h3>
                <p className="text-[#666666] text-base">
                  We're revolutionizing event planning through technology and
                  creative solutions. Join our team to be at the forefront of
                  innovation in the industry.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Open Roles Section */}
      <div id="viewopening" className="py-16 bg-[#F9F9F9]">
        <div className="container xl:max-w-[1120px] mx-auto max-w-5xl px-4">
          <div className="mb-12 text-center">
            <h2 className="text-[#8E8E93] text-[24px] font-medium mb-4 uppercase tracking-wide">
              OPENED ROLES
            </h2>
          </div>

          {/* Job Listings Grid */}
          {true ? (
            <div className="py-10 text-xl font-semibold text-center">
              No opened role at the moment
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {/* Product Designer Job 1 */}
              <div className="overflow-hidden bg-white rounded-xl shadow-sm">
                <div className="flex items-start h-full">
                  <img
                    src="/career-1.svg"
                    alt="Product Designer"
                    className="object-cover w-[72px] h-full"
                  />
                  <div className="p-4">
                    <h3 className="text-lg font-medium text-black">
                      Product Designer
                    </h3>
                    <p className="text-sm text-[#666666] mb-3">
                      Flawless makeup for your big day
                    </p>
                    <div className="flex space-x-2">
                      <span className="bg-[#F4F3FF] text-[#7A5AF8] text-xs font-medium py-1 px-3 rounded-full flex items-center">
                        <span className="w-1.5 h-1.5 bg-[#7A5AF8] rounded-full mr-1.5"></span>
                        Remote
                      </span>
                      <span className="bg-[#F5F5F5] text-[#666666] text-xs font-medium py-1 px-3 rounded-full flex items-center">
                        <span className="w-1.5 h-1.5 bg-[#666666] rounded-full mr-1.5"></span>
                        NG
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Backend Engineer Job */}
              <div className="overflow-hidden bg-white rounded-xl shadow-sm">
                <div className="flex items-start h-full">
                  <img
                    src="/career-2.svg"
                    alt="Backend Engineer"
                    className="object-cover w-[72px] h-full"
                  />
                  <div className="p-4">
                    <h3 className="text-lg font-medium text-black">
                      Backend Engineer
                    </h3>
                    <p className="text-sm text-[#666666] mb-3">
                      Flawless makeup for your big day
                    </p>
                    <div className="flex space-x-2">
                      <span className="bg-[#F4F3FF] text-[#7A5AF8] text-xs font-medium py-1 px-3 rounded-full flex items-center">
                        <span className="w-1.5 h-1.5 bg-[#7A5AF8] rounded-full mr-1.5"></span>
                        Remote
                      </span>
                      <span className="bg-[#F5F5F5] text-[#666666] text-xs font-medium py-1 px-3 rounded-full flex items-center">
                        <span className="w-1.5 h-1.5 bg-[#666666] rounded-full mr-1.5"></span>
                        NG
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Product Designer Job 2 */}
              <div className="overflow-hidden bg-white rounded-xl shadow-sm">
                <div className="flex items-start h-full">
                  <img
                    src="/career-3.svg"
                    alt="Product Designer"
                    className="object-cover w-[72px] h-full"
                  />
                  <div className="p-4">
                    <h3 className="text-lg font-medium text-black">
                      Product Designer
                    </h3>
                    <p className="text-sm text-[#666666] mb-3">
                      Flawless makeup for your big day
                    </p>
                    <div className="flex space-x-2">
                      <span className="bg-[#FFEBF5] text-[#E91E63] text-xs font-medium py-1 px-3 rounded-full flex items-center">
                        <span className="w-1.5 h-1.5 bg-[#E91E63] rounded-full mr-1.5"></span>
                        Remote
                      </span>
                      <span className="bg-[#FFF4EB] text-[#FF5722] text-xs font-medium py-1 px-3 rounded-full flex items-center">
                        <span className="w-1.5 h-1.5 bg-[#FF5722] rounded-full mr-1.5"></span>
                        San Francisco
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Product Designer Job 3 */}
              <div className="overflow-hidden bg-white rounded-xl shadow-sm">
                <div className="flex items-start h-full">
                  <img
                    src="/career-4.svg"
                    alt="Product Designer"
                    className="object-cover w-[72px] h-full"
                  />
                  <div className="p-4">
                    <h3 className="text-lg font-medium text-black">
                      Product Designer
                    </h3>
                    <p className="text-sm text-[#666666] mb-3">
                      Flawless makeup for your big day
                    </p>
                    <div className="flex space-x-2">
                      <span className="bg-[#FFF4EB] text-[#FF5722] text-xs font-medium py-1 px-3 rounded-full flex items-center">
                        <span className="w-1.5 h-1.5 bg-[#FF5722] rounded-full mr-1.5"></span>
                        Remote
                      </span>
                      <span className="bg-[#F5F5F5] text-[#666666] text-xs font-medium py-1 px-3 rounded-full flex items-center">
                        <span className="w-1.5 h-1.5 bg-[#666666] rounded-full mr-1.5"></span>
                        NG
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Product Designer Job 4 */}
              <div className="overflow-hidden bg-white rounded-xl shadow-sm">
                <div className="flex items-start h-full">
                  <img
                    src="/career-5.svg"
                    alt="Product Designer"
                    className="object-cover w-[72px] h-full"
                  />
                  <div className="p-4">
                    <h3 className="text-lg font-medium text-black">
                      Product Designer
                    </h3>
                    <p className="text-sm text-[#666666] mb-3">
                      Flawless makeup for your big day
                    </p>
                    <div className="flex space-x-2">
                      <span className="bg-[#EBF8FF] text-[#2196F3] text-xs font-medium py-1 px-3 rounded-full flex items-center">
                        <span className="w-1.5 h-1.5 bg-[#2196F3] rounded-full mr-1.5"></span>
                        Onsite
                      </span>
                      <span className="bg-[#F5F5F5] text-[#666666] text-xs font-medium py-1 px-3 rounded-full flex items-center">
                        <span className="w-1.5 h-1.5 bg-[#666666] rounded-full mr-1.5"></span>
                        NG
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
}
