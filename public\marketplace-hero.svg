<?xml version="1.0" encoding="UTF-8"?>
<svg width="1440px" height="600px" viewBox="0 0 1440 600" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Marketplace Hero Background</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FEF7F4" offset="0%"></stop>
            <stop stop-color="#EBF3FE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFBBA3" stop-opacity="0.1" offset="0%"></stop>
            <stop stop-color="#A6AAF9" stop-opacity="0.1" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Marketplace-Hero" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="Background" fill="url(#linearGradient-1)" x="0" y="0" width="1440" height="600"></rect>
        
        <!-- Subtle patterns and shapes -->
        <circle cx="200" cy="150" r="80" fill="url(#linearGradient-2)" opacity="0.5"></circle>
        <circle cx="1200" cy="250" r="120" fill="url(#linearGradient-2)" opacity="0.5"></circle>
        <circle cx="600" cy="400" r="100" fill="url(#linearGradient-2)" opacity="0.5"></circle>
        
        <!-- Abstract shapes -->
        <path d="M0,400 C200,350 400,450 600,400 C800,350 1000,450 1200,400 C1400,350 1440,400 1440,400 L1440,600 L0,600 Z" 
              fill="#EBF3FE" opacity="0.3"></path>
        
        <!-- Subtle grid pattern -->
        <g id="Grid" opacity="0.1" stroke="#A6AAF9" stroke-width="0.5">
            <line x1="0" y1="100" x2="1440" y2="100"></line>
            <line x1="0" y1="200" x2="1440" y2="200"></line>
            <line x1="0" y1="300" x2="1440" y2="300"></line>
            <line x1="0" y1="400" x2="1440" y2="400"></line>
            <line x1="0" y1="500" x2="1440" y2="500"></line>
            
            <line x1="100" y1="0" x2="100" y2="600"></line>
            <line x1="300" y1="0" x2="300" y2="600"></line>
            <line x1="500" y1="0" x2="500" y2="600"></line>
            <line x1="700" y1="0" x2="700" y2="600"></line>
            <line x1="900" y1="0" x2="900" y2="600"></line>
            <line x1="1100" y1="0" x2="1100" y2="600"></line>
            <line x1="1300" y1="0" x2="1300" y2="600"></line>
        </g>
        
        <!-- Decorative elements -->
        <g id="Decorative-Elements" opacity="0.2">
            <rect x="120" y="220" width="40" height="40" rx="8" fill="#FFBBA3"></rect>
            <rect x="1280" y="180" width="60" height="60" rx="12" fill="#A6AAF9"></rect>
            <rect x="720" y="320" width="50" height="50" rx="10" fill="#4D55F2"></rect>
            
            <circle cx="400" cy="150" r="15" fill="#FFBBA3"></circle>
            <circle cx="1000" cy="350" r="20" fill="#A6AAF9"></circle>
            <circle cx="600" cy="250" r="10" fill="#4D55F2"></circle>
        </g>
    </g>
</svg>
