import React, { useState, useEffect } from "react";
import { Footer } from "./Footer";

export function TermsOfServiceContent() {
  const [activeSection, setActiveSection] = useState("introduction");

  useEffect(() => {
    // Function to handle hash change
    const handleHashChange = () => {
      const hash = window.location.hash.replace("#", "");
      if (hash) {
        setActiveSection(hash);
      }
    };

    // Function to handle scroll
    const handleScroll = () => {
      const sections = document.querySelectorAll("section[id]");
      let currentSection = "introduction";

      sections.forEach((section) => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        if (
          window.scrollY >= sectionTop - 100 &&
          window.scrollY < sectionTop + sectionHeight - 100
        ) {
          currentSection = section.getAttribute("id");
        }
      });

      setActiveSection(currentSection);
    };

    // Add event listeners
    window.addEventListener("hashchange", handleHashChange);
    window.addEventListener("scroll", handleScroll);

    // Initial check
    handleHashChange();

    // Cleanup
    return () => {
      window.removeEventListener("hashchange", handleHashChange);
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);
  return (
    <div className="w-full">
      {/* Hero Section */}

      <div
        className="relative w-full h-[540px] md:h-[850px] pt-16 flex flex-col items-center justify-start "
        style={{
          backgroundImage:
            "linear-gradient(#F5F6FE , #fff), url('/terms-of-service.jpg')",
          backgroundSize: "cover",
          backgroundPosition: "left",
          backgroundBlendMode: "soft-light",
        }}
      >
        {/* Overlay
        <div className="absolute inset-0 bg-[#F5F6FE] opacity-90"></div> */}
        <div className="flex justify-center items-center mb-8">
          <div className="flex items-center">
            <img src="/logo.svg" alt="EventPark" className="h-6" />
            <span className="text-[#4D55F2] font-bold text-[24px] ml-2">
              EventPark.
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="relative z-10 px-4 mt-[40px] max-w-4xl text-center">
          <p className="text-[#4D55F2] uppercase tracking-wider mb-6 text-[18px] ">
            TERMS OF SERVICE
          </p>
          <h1 className="mb-4 text-5xl font-bold">
            <span className="text-[#CACCFB] text-[48px] md:text-[100px]">
              Use EventPark
            </span>
            <br />
            <span className="text-[#000073] text-[48px] md:text-[130px]">
              The right way
            </span>
          </h1>
          <p className="text-[#8E8E93] md:max-w-xl mx-auto mb-3 md:mb-6 text-[18px] md:text-[24px]">
            We protect your data with utmost
            <br />
            transparency and care.
          </p>
          <p className="text-xs flex items-center w-fit mx-auto text-[#4D55F2] bg-[#F5F6FE] font-bold rounded-[30px] gap-1 px-3 py-1">
            <span className="text-[#7A5AF8] w-4 h-4">•</span>{" "}
            <span className="text-[#5925DC]">Last updated:</span>{" "}
            <span className="text-[black]">10 January 2024</span>
          </p>
        </div>
      </div>

      <div className="">
        <div className="mx-auto max-w-7xl mt-[-100px] relative z-[100]">
          <nav className="flex justify-center">
            <ul className="flex px-8 py-3 space-x-12 bg-white rounded-full shadow-lg">
              <li>
                <a
                  href="/"
                  className="text-[10px] md:text-sm font-medium text-black hover:text-[#4D55F2]"
                >
                  HOME
                </a>
              </li>
              {/* <li>
                <a
                  href=""
                  className="text-[10px] md:text-sm font-medium text-black hover:text-[#4D55F2]"
                >
                  COMPANY
                </a>
              </li> */}
              <li>
                <a
                  href="/vendor"
                  className="text-[10px] md:text-sm font-medium text-black hover:text-[#4D55F2]"
                >
                  FOR VENDORS
                </a>
              </li>
              <li>
                <a
                  href="/marketplace"
                  className="text-[10px] md:text-sm font-medium text-black hover:text-[#4D55F2]"
                >
                  MARKETPLACE
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
      <div
        className="h-[80px] w-full bg-cover bg-no-repeat"
        style={{ backgroundImage: "url('/terms-rectangle.svg')" }}
      ></div>

      {/* Main Content */}
      <div className="container px-4 py-16 mt-[200px\] mx-auto max-w-7xl">
        <div className="flex flex-col gap-12 md:flex-row">
          {/* Sidebar */}
          <div className="w-full md:w-1/5">
            {/* <div className="bg-[#4D55F2] text-white py-2 px-4 rounded-full mb-6 text-center text-sm">
              <span>Contents</span>
            </div> */}
            <nav className="sticky top-8">
              <ul className="p-4 space-y-4 text-xs border border-t-0 border-gray-100">
                {[
                  { id: "introduction", label: "Introduction" },
                  { id: "acceptance", label: "Acceptance of Terms" },
                  { id: "definitions", label: "Definitions" },
                  { id: "eligibility", label: "Eligibility" },
                  { id: "account", label: "Account Responsibilities" },
                  { id: "use", label: "Use of the Platform" },
                  { id: "booking", label: "Booking and Payments" },
                  { id: "contente", label: "Content Ownership and License" },
                  { id: "dispute", label: "Dispute Resolution" },
                  { id: "termination", label: "Termination" },
                  { id: "limitation", label: "Limitation of Liability" },
                  { id: "indemnification", label: "Indemnification" },
                  { id: "changes", label: "Changes to These Terms" },
                  { id: "governing", label: "Governing Law" },
                  { id: "contact", label: "Contact Us" },
                ].map((item, index, array) => (
                  <li
                    key={item.id}
                    className={`py-2 ${
                      index < array.length - 1 ? "border-b border-gray-100" : ""
                    }`}
                  >
                    <a
                      href={`#${item.id}`}
                      className={`block ${
                        activeSection === item.id
                          ? "text-white font-medium bg-[#4D55F2] rounded-full px-3 py-1 h-[30px] flex items-center"
                          : "text-[#666666] hover:text-[#4D55F2] px-3 py-1"
                      }`}
                      onClick={() => setActiveSection(item.id)}
                    >
                      {item.label}
                    </a>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* Terms Content */}
          <div className="py-8 w-full border border-t-0 border-gray-100 md:w-3/4">
            <section id="introduction" className="px-8 pb-8 mb-12">
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Introduction
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                Welcome to EventPark! By accessing or using our platform, you
                agree to comply with these Terms of Service ("Terms"). Please
                read them carefully, as they outline your rights,
                responsibilities, and obligations when using EventPark.
              </p>
            </section>

            <section
              id="acceptance"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Acceptance of Terms
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                By creating an account or using EventPark, you agree to these
                Terms, our Privacy Policy, and any additional guidelines or
                policies posted on the platform. If you do not agree, you must
                not use EventPark.
              </p>
            </section>

            <section
              id="definitions"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Definitions
              </h2>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>
                  <strong>"EventPark"</strong> refers to the platform, website,
                  and related services provided by [Company Name]
                </li>
                <li>
                  <strong>"Users"</strong> include both event planners and
                  vendors who access or use the platform.
                </li>
                <li>
                  <strong>"Vendors"</strong> are businesses or individuals
                  offering services through EventPark.
                </li>
                <li>
                  <strong>"Event Planners"</strong> are users seeking to book
                  services from vendors.
                </li>
              </ul>
            </section>

            <section
              id="eligibility"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Eligibility
              </h2>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>You must be at least 18 years old to use EventPark.</li>
                <li>
                  By using EventPark, you represent and warrant that all
                  information you provide is accurate, complete, and current.
                </li>
              </ul>
            </section>

            <section
              id="account"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Account Responsibilities
              </h2>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>
                  Users must create an account to access certain features.
                </li>
                <li>
                  You are responsible for maintaining the confidentiality of
                  your login credentials and for all activities under your
                  account.
                </li>
                <li>
                  Notify EventPark immediately of any unauthorized access or
                  breach of security.
                </li>
              </ul>
            </section>

            <section
              id="use"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Use of the Platform
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                Prohibited Activities: You agree not to:
              </p>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>
                  Use the platform for illegal, fraudulent, or unauthorized
                  purposes.
                </li>
                <li>Post false, misleading, or defamatory content.</li>
                <li>Interfere with the operation or security of EventPark.</li>
              </ul>
              <p className="text-[#666666] mt-4 mb-4 text-sm leading-relaxed">
                Vendor Listings: Vendors must provide accurate descriptions of
                their services, pricing, and availability.
              </p>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                Event Planners: Event planners must use the platform solely for
                booking legitimate vendor services.
              </p>
            </section>

            <section
              id="booking"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Booking and Payments
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                Booking Process: Event planners can request bookings through
                EventPark, and vendors have the right to accept or decline.
              </p>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                Payments: Payments are facilitated through secure third-party
                payment processors. EventPark does not store payment
                information.
              </p>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                Cancellation and Refunds: Cancellation and refund policies vary
                by vendor and will be outlined in the vendor's terms. Users are
                responsible for reviewing these policies before booking.
              </p>
            </section>

            <section
              id="contente"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Content Ownership and License
              </h2>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>
                  All content on EventPark, including text, graphics, logos, and
                  software, is the property of EventPark or its licensors and is
                  protected by intellectual property laws.
                </li>
                <li>
                  Users retain ownership of content they upload but grant
                  EventPark a license to use, display, and distribute such
                  content on the platform.
                </li>
                <li>
                  You must not use content from EventPark without prior written
                  permission.
                </li>
              </ul>
            </section>

            <section
              id="dispute"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Dispute Resolution
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                EventPark is not a party to contracts between vendors and event
                planners.
              </p>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                Users are encouraged to resolve disputes directly. If necessary,
                EventPark may offer mediation services but is not obligated to
                do so.
              </p>
            </section>

            <section
              id="termination"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Termination
              </h2>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>
                  EventPark reserves the right to terminate or suspend accounts
                  that violate these Terms or for any other reason at our sole
                  discretion.
                </li>
                <li>
                  Users may also cancel their accounts at any time by using the
                  "Delete Account" option in settings.
                </li>
              </ul>
            </section>

            <section
              id="limitation"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Limitation of Liability
              </h2>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>
                  EventPark is not liable for any indirect, incidental, or
                  consequential damages.
                </li>
                <li>
                  Our liability is limited to the lesser of (a) the amount paid
                  by you to EventPark in the 12 months preceding the claim, or
                  (b) $100.
                </li>
                <li>
                  Some jurisdictions do not allow the exclusion of certain
                  warranties, so some limitations may not apply to you.
                </li>
              </ul>
            </section>

            <section
              id="indemnification"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Indemnification
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                You agree to indemnify and hold EventPark harmless from any
                claims, damages, or expenses arising from your use of the
                platform, your content, or your violation of these Terms.
              </p>
            </section>

            <section
              id="changes"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Changes to These Terms
              </h2>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>
                  EventPark may modify these Terms at any time. We will notify
                  users of significant changes via email or through the
                  platform.
                </li>
                <li>
                  Your continued use of EventPark after such modifications
                  constitutes acceptance of the updated Terms.
                </li>
              </ul>
            </section>

            <section
              id="governing"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Governing Law
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                These Terms are governed by the laws of [Insert Jurisdiction].
                Any disputes will be resolved exclusively in the courts of
                [Insert Jurisdiction].
              </p>
            </section>

            <section
              id="contact"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Contact Us
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                If you have any questions or concerns about these Terms, please
                contact us at:
              </p>
              <p className="text-[#666666] text-sm mb-2">
                <strong>EventPark Legal Team</strong>
              </p>
              <p className="text-[#666666] text-sm mb-2">
                <strong>Email:</strong> <EMAIL>
              </p>
              <p className="text-[#666666] text-sm">
                <strong>Phone Number:</strong> +234 800 EVENT PARK
              </p>
            </section>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
