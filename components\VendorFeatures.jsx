import React from "react";

export function VendorFeatures() {
  return (
    <div className="py-16 w-full bg-white">
      <div className="container px-4 mx-auto max">
        {/* Section Title */}

        {/* Features Grid */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 sm:grid-rows-2 lg:h-[584px]">
          {/* Organized Communication Channel */}
          <div className="relative  p-8 bg-[#F5F9FF] rounded-3xl sm:row-span-2 lg:col-span-1">
            <div className="overflow-hidden absolute top-0 left-0 w-full h-full rounded-3xl">
              <div className="hidden absolute top-0 left-0 w-full h-full sm:block">
                <img
                  src="/coil-31.svg"
                  alt=""
                  className="object-cover w-full h-full"
                />
              </div>
            </div>
            <div className="relative z-10">
              <div className="flex justify-center items-center mb-6 w-[56px] h-[56px] bg-[#CDE0FD] rounded-full">
                <svg
                  width="24"
                  height="25"
                  viewBox="0 0 24 25"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M11.9999 4.83329C15.8659 4.83329 18.9999 7.9673 18.9999 11.8333C18.9999 14.4243 17.5922 16.6865 15.4999 17.8968L15.4999 22.3333L14.3333 23.5H13.1666C13.1666 24.1443 12.6443 24.6666 11.9999 24.6666C11.3556 24.6666 10.8333 24.1443 10.8333 23.5H9.66659L8.49992 22.3333L8.4999 17.8968C6.4076 16.6865 4.99992 14.4243 4.99992 11.8333C4.99992 7.9673 8.13392 4.83329 11.9999 4.83329ZM13.1666 18.7365C12.7872 18.8002 12.3974 18.8333 11.9999 18.8333C11.6024 18.8333 11.2126 18.8002 10.8332 18.7365L10.8333 21.1666H13.1666L13.1666 18.7365ZM5.60649 16.9893L6.84393 18.2267L4.36906 20.7016L3.13162 19.4642L5.60649 16.9893ZM18.3933 16.9893L20.8682 19.4642L19.6308 20.7016L17.1559 18.2267L18.3933 16.9893ZM23.6666 10.9583V12.7083H20.1666V10.9583H23.6666ZM3.83325 10.9583V12.7083H0.333252V10.9583H3.83325ZM19.6308 2.965L20.8682 4.20243L18.3933 6.67731L17.1559 5.43987L19.6308 2.965ZM4.36906 2.965L6.84393 5.43987L5.60649 6.67731L3.13162 4.20243L4.36906 2.965ZM12.8749 0.166626V3.66663H11.1249V0.166626H12.8749Z"
                    fill="#5075AF"
                  />
                </svg>
              </div>
              <h3 className="mb-1 text-[#1D427D] text-[32px] xl:text-[48px] tracking-tight font-medium">
                Organized
              </h3>
              <h3 className="mb-6 text-[32px] xl:text-[48px] tracking-tight text-[#9CC1FC]  font-medium">
                Communication Channel
              </h3>
              <p className="text-[#8E8E93] font-medium text-[18px]">
                Seamless chats to keep every detail in check.
              </p>
            </div>
          </div>

          {/* Analytical Insights */}
          <div className="relative p-8 bg-[#F5F9FF] rounded-3xl">
            <img
              src="/vendor-2.svg"
              alt=""
              className="hidden absolute top-0 right-0 left-0 lg:block"
            />
            <div className="flex justify-center items-center mb-6 w-[56px] h-[56px] bg-[#DBDDFC] rounded-full">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 20V10"
                  stroke="#4D55F2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M12 20V4"
                  stroke="#4D55F2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M6 20V14"
                  stroke="#4D55F2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
            <h3 className="mb-4 text-black text-[28px] font-medium mt-[56px]">
              Analytical Insights
            </h3>
            <p className="text-[#8E8E93] w-[274px]">
              Track performance and make data-driven decisions.
            </p>
          </div>

          {/* Secure wallet system */}
          <div className="relative p-8 bg-[#FFFCFB] rounded-3xl">
            <img
              src="/vendor-coil-5.svg"
              alt=""
              className="hidden absolute top-0 left-0 lg:block"
            />
            <div className="flex justify-center items-center mb-6 w-[56px] h-[56px] bg-[#FDEFE9] rounded-full">
              <svg
                width="28"
                height="28"
                viewBox="0 0 28 28"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M24.4649 18.76C24.1849 21.875 21.9333 23.9167 18.6666 23.9167H8.16659C4.94659 23.9167 2.33325 21.3034 2.33325 18.0834V9.91671C2.33325 6.74337 4.24659 4.52671 7.22159 4.15337C7.52492 4.10671 7.83992 4.08337 8.16659 4.08337H18.6666C18.9699 4.08337 19.2616 4.09504 19.5416 4.14171C22.3299 4.46837 24.2199 6.41671 24.4649 9.24004C24.4999 9.57837 24.2199 9.85837 23.8816 9.85837H22.0733C20.9533 9.85837 19.9149 10.29 19.1683 11.06C18.2816 11.9234 17.8383 13.1367 17.9433 14.35C18.1299 16.4734 19.9966 18.1417 22.2133 18.1417H23.8816C24.2199 18.1417 24.4999 18.4217 24.4649 18.76Z"
                  fill="#C9B2A8"
                />
                <path
                  d="M25.6666 12.7983V15.2016C25.6666 15.8433 25.1533 16.3683 24.4999 16.3916H22.2133C20.9533 16.3916 19.7983 15.4699 19.6933 14.2099C19.6233 13.4749 19.9033 12.7866 20.3933 12.3083C20.8249 11.8649 21.4199 11.6083 22.0733 11.6083H24.4999C25.1533 11.6316 25.6666 12.1566 25.6666 12.7983Z"
                  fill="#C9B2A8"
                />
              </svg>
            </div>
            <h3 className="mb-4 text-black text-[28px] font-medium mt-[56px]">
              Secure wallet system
            </h3>
            <p className="text-[#8E8E93] w-[274px]">
              Safe, easy transactions all in one place.
            </p>
          </div>

          {/* Automated Payouts */}
          <div className="relative p-8 bg-[#FEF7F4] rounded-3xl">
            <img
              src="/vendor-coil-3.svg"
              alt=""
              className="hidden absolute top-0 left-0 lg:block"
            />
            <div className="flex justify-center items-center mb-6 w-[56px] h-[56px] bg-[#E2CBC1] rounded-full">
              <svg
                width="28"
                height="28"
                viewBox="0 0 28 28"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M14.875 18.5733H15.6333C16.3917 18.5733 17.0217 17.8966 17.0217 17.08C17.0217 16.065 16.66 15.8666 16.065 15.6566L14.8867 15.2483V18.5733H14.875Z"
                  fill="black"
                />
                <path
                  d="M13.965 2.21669C7.525 2.24003 2.31 7.47836 2.33333 13.9184C2.35666 20.3584 7.595 25.5734 14.035 25.55C20.475 25.5267 25.69 20.2884 25.6667 13.8484C25.6433 7.40836 20.405 2.20503 13.965 2.21669ZM16.6367 14C17.5467 14.315 18.7717 14.9917 18.7717 17.08C18.7717 18.8767 17.36 20.3234 15.6333 20.3234H14.875V21C14.875 21.4784 14.4783 21.875 14 21.875C13.5217 21.875 13.125 21.4784 13.125 21V20.3234H12.705C10.7917 20.3234 9.24 18.7134 9.24 16.73C9.24 16.2517 9.63666 15.855 10.115 15.855C10.5933 15.855 10.99 16.2517 10.99 16.73C10.99 17.745 11.76 18.5734 12.705 18.5734H13.125V14.63L11.3633 14C10.4533 13.685 9.22833 13.0084 9.22833 10.92C9.22833 9.12336 10.64 7.67669 12.3667 7.67669H13.125V7.00003C13.125 6.52169 13.5217 6.12503 14 6.12503C14.4783 6.12503 14.875 6.52169 14.875 7.00003V7.67669H15.295C17.2083 7.67669 18.76 9.28669 18.76 11.27C18.76 11.7484 18.3633 12.145 17.885 12.145C17.4067 12.145 17.01 11.7484 17.01 11.27C17.01 10.255 16.24 9.42669 15.295 9.42669H14.875V13.37L16.6367 14Z"
                  fill="#967F75"
                />
                <path
                  d="M10.99 10.9316C10.99 11.9466 11.3517 12.145 11.9467 12.355L13.125 12.7633V9.42664H12.3667C11.6083 9.42664 10.99 10.1033 10.99 10.9316Z"
                  fill="#967F75"
                />
              </svg>
            </div>
            <h3 className="mb-4 text-black text-[28px] font-medium mt-[56px]">
              Automated Payouts
            </h3>
            <p className="text-[#8E8E93] w-[274px]">
              Get paid on time, every time—hassle-free
            </p>
          </div>

          {/* So much more... */}
          <div className="relative p-8 bg-[#E1ECFE] rounded-3xl ">
            <img
              src="/vendor-coil-4.svg"
              alt=""
              className="hidden absolute top-0 left-0 lg:block"
            />
            <div className="hidden absolute top-0 left-0 lg:block"></div>
            <div className="flex justify-center items-center mb-6 w-[56px] h-[56px] bg-[#C4DAFD] rounded-full">
              <svg
                width="28"
                height="28"
                viewBox="0 0 28 28"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M10.5001 16.59H4.57342C3.86176 16.59 3.20842 16.9516 2.83509 17.5583C2.46176 18.1533 2.42676 18.865 2.73009 19.495C4.16509 22.435 6.75509 24.745 9.83509 25.83C10.0451 25.9 10.2784 25.9466 10.5001 25.9466C10.9084 25.9466 11.3168 25.8183 11.6668 25.5733C12.2151 25.1883 12.5418 24.5583 12.5418 23.8933L12.5534 18.6433C12.5534 18.095 12.3434 17.5816 11.9584 17.1966C11.5618 16.8116 11.0484 16.59 10.5001 16.59Z"
                  fill="#5075AF"
                />
                <path
                  d="M26.2267 11.2C24.92 5.46004 19.8917 1.45837 14 1.45837C8.10833 1.45837 3.08 5.46004 1.77333 11.2C1.63333 11.8067 1.77333 12.425 2.17 12.915C2.56666 13.405 3.15 13.685 3.78 13.685H24.2317C24.8617 13.685 25.445 13.405 25.8417 12.915C26.2267 12.425 26.3667 11.795 26.2267 11.2Z"
                  fill="#5075AF"
                />
                <path
                  d="M23.4033 16.6484L17.4999 16.6367C16.9516 16.6367 16.4383 16.8467 16.0533 17.2317C15.6683 17.6167 15.4583 18.1301 15.4583 18.6784L15.4699 23.9051C15.4699 24.5701 15.7966 25.2001 16.3449 25.5851C16.6949 25.8301 17.1033 25.9584 17.5116 25.9584C17.7333 25.9584 17.9549 25.9234 18.1649 25.8417C21.2216 24.7684 23.8116 22.4701 25.2466 19.5651C25.5499 18.9467 25.5149 18.2234 25.1533 17.6401C24.7683 17.0101 24.1149 16.6484 23.4033 16.6484Z"
                  fill="#5075AF"
                />
              </svg>
            </div>
            <h3 className="mb-4 text-black text-[28px] font-medium mt-[56px]">
              So much more...
            </h3>
            <p className="text-[#8E8E93] w-[274px]">
              We create a platform that connects and empowers everyone
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// export const waitlistApi = {
//   getWaitlist: async (filters?: WaitlistFilters): Promise<WaitlistResponse> => {
//     const response = await axiosInstance.get(`${BASE_URL}/v1/waitlist`, {
//       params: filters,
//     });
//     return response.data;
//   },  Parameters
// Try it out
// Name	Description
// req *
// object
// (body)
// Body

// Example Value
// Model
// {
//   "email": "<EMAIL>",
//   "first_name": "string",
//   "joining_as": "user",
//   "last_name": "string"
// }
// Parameter content type , VITE_API_BASE_URL=https://api-dev.eventpark.africa  need it integrated
