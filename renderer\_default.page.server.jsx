// renderer/_default.page.server.jsx
import React from "react";
import ReactDOMServer from "react-dom/server";
import { escapeInject, dangerouslySkipEscape } from "vite-plugin-ssr/server";
import { PageLayout } from "./PageLayout";
import { PageContextProvider } from "./usePageContext";

export { render };
export { passToClient };

// See https://vite-plugin-ssr.com/data-fetching
const passToClient = ["pageProps", "documentProps", "urlPathname"];

async function render(pageContext) {
  const { Page, pageProps } = pageContext;

  // This render() hook only supports pages that export a React component as their default export
  if (!Page)
    throw new Error("My render() hook expects pageContext.Page to be defined");

  const pageHtml = ReactDOMServer.renderToString(
    <PageContextProvider pageContext={pageContext}>
      <PageLayout>
        <Page {...pageProps} />
      </PageLayout>
    </PageContextProvider>
  );

  // See https://vite-plugin-ssr.com/html-head
  const { documentProps } = pageContext;
  const title = (documentProps && documentProps.title) || "Event Park Website";
  const description =
    (documentProps && documentProps.description) || "Event Park Website";

  const documentHtml = escapeInject`<!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        
    <link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
<link rel="shortcut icon" href="/favicon.ico" />
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
<meta name="apple-mobile-web-app-title" content="MyWebSite" />
<link rel="manifest" href="/site.webmanifest" />
        <meta description="EventPark is your all-in-one platform for discovering
    vendors, managing guests, and organizing every detail seamlessly." />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>
      Plan unforgettable events with ease. EventPark is your all-in-one platform
      for discovering vendors, managing guests, and organizing every detail
      seamlessly.
    </title>

        <meta name="color-scheme" content="light only" />
        <meta name="supported-color-schemes" content="light" />
        <meta name="theme-color" content="#FFFFFF" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="renderer" content="webkit" />
       
        <script>
          // Disable Samsung Internet force dark mode
          (function() {
            try {
              // Set color-scheme to light only
              document.documentElement.style.colorScheme = 'light only';

              // Force light mode for Samsung Internet
              if (navigator.userAgent.includes('SamsungBrowser')) {
                // Create a style element to override dark mode
                var style = document.createElement('style');
                style.textContent = 'html, body { background-color: #FFFFFF !important; color: #000000 !important; } * { -webkit-forced-color-adjust: none !important; forced-color-adjust: none !important; }';
                document.head.appendChild(style);
              }
            } catch(e) {
              console.error('Error disabling dark mode:', e);
            }
          })();
        </script>
      </head>
      <body>
        <div id="page-view">${dangerouslySkipEscape(pageHtml)}</div>
      </body>
    </html>`;

  return {
    documentHtml,
    pageContext: {
      // We can add some `pageContext` here, which is useful if we want to do page transitions
    },
  };
}
