import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { waitlistApi } from "../services/api";

export default function WaitlistModal({ onClose }) {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [businessName, setBusinessName] = useState("");
  const [category, setCategory] = useState("Catering");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Images for the left side of the modal
  const waitlistImages = ["/vendor-waitlist-1.svg", "/vendor-waitlist-2.svg"];

  // Categories for the dropdown
  const categories = [
    "Catering",
    "Photography",
    "Venue",
    "Decoration",
    "Entertainment",
    "Wedding Planning",
    "Makeup Artist",
    "Transportation",
  ];

  // Auto-change images every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === waitlistImages.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const [error, setError] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Prepare the data according to the API requirements
      const waitlistData = {
        email: email,
        first_name: firstName,
        last_name: lastName,
        joining_as: "vendor", // Since this is the vendor waitlist
        business_name: businessName, // Additional field for business name
        category: category, // Additional field for vendor category
      };

      // Call the API to add to waitlist
      const response = await waitlistApi.addToWaitlist(waitlistData);

      console.log("Waitlist submission successful:", response);
      setIsSubmitting(false);
      setIsSuccess(true);
    } catch (err) {
      console.error("Waitlist submission error:", err);
      setError(
        err.response?.data?.message ||
          "Failed to join waitlist. Please try again."
      );
      setIsSubmitting(false);
    }
  };

  const handleShare = (platform) => {
    // Implement sharing functionality based on the platform
    console.log(`Sharing to ${platform}`);
    // In a real implementation, you would use the Web Share API or platform-specific sharing
  };

  return (
    <div className="fixed inset-0 bg-black/30 filter backdrop-blur-[5px] flex items-center justify-center z-50 px-4">
      <AnimatePresence mode="wait">
        {isSuccess ? (
          // Success Screen
          <motion.div
            key="success"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-[24px] md:h-[600px] h-full max-h-[98%] max-w-[800px] w-full relative overflow-y-auto  flex flex-col md:flex-row"
          >
            {/* Close button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 w-[32px] h-[32px] flex items-center justify-center rounded-full bg-white/80 text-gray-400 hover:text-gray-600 z-10"
            >
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 6L6 18"
                  stroke="#4D55F2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M6 6L18 18"
                  stroke="#4D55F2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>

            {/* Left side - Image */}
            <div className="hidden md:block md:w-[38%] bg-[#FF7F50] relative overflow-hidden">
              <img
                src="/vendor-waitlist-1.svg"
                alt="Vendor waitlist"
                className="object-cover w-full h-full"
              />
            </div>

            {/* Right side - Success content */}
            <div className="flex flex-col items-center relative justify-center p-8 md:w-[62%]">
              <div className="w-full max-w-[330px] text-center">
                <img
                  src="/success-waitlist.gif"
                  alt="Success"
                  className="mx-auto mb-6 w-[600px] h-[300px] absolute top-0"
                />
                <div className="flex justify-center items-center w-full">
                  <div className="w-[150px] h-[150px] flex justify-center items-center rounded-[500px] border border-[#EDEEFE]">
                    <div className="bg-[#F5F6FE] flex justify-center items-center text-[80px] leading-[100%] w-[112px] h-[112px] rounded-[500px]">
                      🎉
                    </div>
                  </div>
                </div>
                <h2 className="mb-2 text-[28px] font-bold">
                  You're on the List! 🥳
                </h2>
                <p className="mb-8 text-gray-600">
                  You're now on the waitlist for early access to EventPark. Stay
                  tuned for updates and exclusive perks!
                </p>

                <div className="mb-8">
                  <h3 className="mb-6 pt-7 border-t border-t-[#F5F5F5] text-sm font-medium tracking-wider text-gray-500 uppercase">
                    SHARE TO FRIENDS
                  </h3>
                  <div className="flex justify-center space-x-4">
                    {/* <a
                      href={
                        import.meta.env.VITE_FACEBOOK_URL ||
                        "https://www.facebook.com/eventparkafrica/"
                      }
                    >
                      <button
                        onClick={() => handleShare("facebook")}
                        className="w-10 h-10 rounded-full bg-[#F5F6FE] flex items-center justify-center hover:text-[#d1d6ff]  e transition-colors"
                      >
                        <img src="/facebook1.svg" alt="" />
                      </button>
                    </a> */}
                    <a
                      href={
                        import.meta.env.VITE_INSTAGRAM_URL ||
                        "https://www.instagram.com/eventparkafrica/"
                      }
                    >
                      <button
                        onClick={() => handleShare("instagram")}
                        className="w-10 h-10 rounded-full bg-[#F5F6FE] flex items-center justify-center hover:text-[#d1d6ff]  e transition-colors"
                      >
                        <img src="/instagram.svg" alt="" />
                        {/* <span className="sr-only">Share on Instagram</span>i */}
                      </button>
                    </a>
                    <a
                      target="_blank"
                      href={
                        import.meta.url.VITE_TIKTOK_URL ||
                        "https://vm.tiktok.com/ZMBWP9YLU/"
                      }
                    >
                      <button
                        onClick={() => handleShare("tiktok")}
                        className="w-10 h-10 rounded-full bg-[#F5F6FE] flex items-center justify-center hover:text-[#d1d6ff]  e transition-colors"
                      >
                        <img src="/tiktok.svg" alt="" />
                        {/* <span className="sr-only">Share on TikTok</span>t */}
                      </button>
                    </a>
                    <a
                      target="_blank"
                      href={
                        import.meta.url.VITE_LINKEDIN_URL ||
                        "https://www.linkedin.com/company/eventpark-ltd"
                      }
                    >
                      <button
                        onClick={() => handleShare("linkedin")}
                        className="w-10 h-10 rounded-full bg-[#F5F6FE] flex items-center justify-center hover:text-[#d1d6ff]  e transition-colors"
                      >
                        {/* <span className="sr-only">Share on LinkedIn</span>
                      in */}{" "}
                        <img src="/linkedin.svg" alt="" />
                      </button>
                    </a>
                    <a
                      target="_blank"
                      href={
                        import.meta.url.VITE_TWITTER_URL ||
                        "https://twitter.com/EventParkAfrica"
                      }
                    >
                      <button
                        onClick={() => handleShare("linkedin")}
                        className="w-10 h-10 rounded-full bg-[#F5F6FE] flex items-center justify-center hover:text-[#d1d6ff]  e transition-colors"
                      >
                        {/* <span className="sr-only">Share on LinkedIn</span>
                      in */}{" "}
                        <img src="/twitter.svg" alt="" />
                      </button>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ) : (
          // Form Screen
          <motion.div
            key="form"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-[24px] h-full md:h-[600px] max-h-[96%] max-w-[800px] w-full relative overflow-y-auto md:overflow-y-hidden flex flex-col md:flex-row"
          >
            {/* Close button */}
            <button
              onClick={onClose}
              className="absolute top-4 cursor-pointer right-4 w-[32px] h-[32px] flex items-center justify-center rounded-full bg-[#4D55F2]/40 text-gray-400 hover:text-gray-600 z-10"
            >
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18 6L6 18"
                  stroke="#4D55F2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M6 6L18 18"
                  stroke="#4D55F2"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>

            {/* Left side - Image */}
            <div className="hidden relative md:block md:w-[38%] overflow-hidden">
              <AnimatePresence mode="wait">
                <motion.img
                  key={currentImageIndex}
                  src={waitlistImages[currentImageIndex]}
                  alt="Join the waitlist"
                  className="object-cover absolute inset-0 w-full h-full"
                  initial={{ opacity: 0.7 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0.7 }}
                  transition={{
                    duration: 0.5,
                    easings: ["easeInOut", "easeOut"],
                  }}
                />
              </AnimatePresence>
            </div>

            {/* Right side - Form */}
            <div className="flex flex-col p-8 md:w-[62%]  md:overflow-y-auto">
              <div className="mt-5 mb-6">
                <h3 className="text-[13px] uppercase tracking-[2px] text-[#808080] mb-[2px]">
                  STAND OUT OF THE CROWD
                </h3>
                <h2 className="text-[28px] font-bold text-black">
                  Join the Waitlist
                </h2>
              </div>

              <form
                onSubmit={handleSubmit}
                className="flex flex-col gap-6 pb-6"
              >
                {/* First Name and Last Name fields */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  {/* First Name field */}
                  <div className="flex flex-col gap-2">
                    <label
                      htmlFor="firstName"
                      className="text-sm text-[#414651] font-medium"
                    >
                      First Name
                    </label>
                    <input
                      type="text"
                      id="firstName"
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      placeholder="John"
                      required
                      className="h-[44px] w-full px-3 py-2 placeholder:text-[#B3B3B3] border border-[#D5D7DA] rounded-[64px] focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {/* Last Name field */}
                  <div className="flex flex-col gap-2">
                    <label
                      htmlFor="lastName"
                      className="text-sm text-[#414651] font-medium"
                    >
                      Last Name
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      placeholder="Doe"
                      required
                      className="h-[44px] w-full px-3 py-2 placeholder:text-[#B3B3B3] border border-[#D5D7DA] rounded-[64px] focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                {/* Email field */}
                <div className="flex flex-col gap-2">
                  <label
                    htmlFor="email"
                    className="text-sm text-[#414651] font-medium"
                  >
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    required
                    className="h-[44px] w-full px-3 py-2 placeholder:text-[#B3B3B3] border border-[#D5D7DA] rounded-[64px] focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-[63%_35%]">
                  {/* Business name field */}
                  <div className="flex flex-col gap-2">
                    <label
                      htmlFor="businessName"
                      className="text-sm text-[#414651] font-medium"
                    >
                      Business name
                    </label>
                    <input
                      type="text"
                      id="businessName"
                      value={businessName}
                      onChange={(e) => setBusinessName(e.target.value)}
                      placeholder="Enter your business name"
                      required
                      className="h-[44px] w-full px-3 py-2 placeholder:text-[#B3B3B3] border border-[#D5D7DA] rounded-[64px] focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {/* Category dropdown */}
                  <div className="flex flex-col gap-2">
                    <label
                      htmlFor="category"
                      className="text-sm text-[#414651] font-medium"
                    >
                      Category
                    </label>
                    <div className="relative">
                      <select
                        id="category"
                        value={category}
                        onChange={(e) => setCategory(e.target.value)}
                        className="appearance-none h-[44px] w-full px-3 py-2 placeholder:text-[#B3B3B3] border border-[#D5D7DA] rounded-[64px] focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {categories.map((cat) => (
                          <option key={cat} value={cat}>
                            {cat}
                          </option>
                        ))}
                      </select>
                      <div className="flex absolute inset-y-0 right-0 items-center px-2 pointer-events-none">
                        <svg
                          width="12"
                          height="8"
                          viewBox="0 0 12 8"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M1 1.5L6 6.5L11 1.5"
                            stroke="#808080"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Error message */}
                {error && (
                  <div className="p-3 mb-4 text-sm text-red-700 bg-red-100 rounded-lg">
                    {error}
                  </div>
                )}

                {/* Submit button */}
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`mt-4 py-3 px-3  flex justify-center items-center rounded-full w-[204px] h-[48px] font-semibold text-white ${
                    isSubmitting
                      ? "bg-[#03286364] cursor-not-allowed"
                      : "bg-[#4D55F2] cursor-pointer hover:bg-[#3A42D1]"
                  } transition-colors flex items-center justify-center`}
                >
                  {isSubmitting ? (
                    <span className="flex gap-2 items-center">
                      <img src="/cardano.svg" alt="" />
                      Joining Waitlist...
                    </span>
                  ) : (
                    <>
                      Be the first to know! <span className="ml-2">👌</span>
                    </>
                  )}
                </button>
              </form>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
