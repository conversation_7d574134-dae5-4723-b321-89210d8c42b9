import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";

export default function NavigationProgress() {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const location = useLocation();

  useEffect(() => {
    // Show the progress bar and start progress animation when navigation starts
    const startNavigation = () => {
      setIsVisible(true);
      setProgress(0);

      // Quickly move to 30%
      setTimeout(() => setProgress(30), 100);

      // Then more slowly to 70%
      setTimeout(() => setProgress(70), 500);

      // Then even more slowly to 90%
      setTimeout(() => setProgress(90), 1000);
    };

    // Complete the progress and hide the bar when navigation completes
    const completeNavigation = () => {
      setProgress(100);

      // Hide after the completion animation
      setTimeout(() => {
        setIsVisible(false);
        setProgress(0);
      }, 500);
    };

    // Start navigation progress
    startNavigation();

    // Simulate navigation completion after a short delay
    // In a real app, this would be triggered by router events
    const timer = setTimeout(() => {
      completeNavigation();
    }, 1500);

    return () => clearTimeout(timer);
  }, [location.pathname]); // Re-run when the path changes

  return (
    <div
      className={`fixed top-0 left-0 right-0 h-1 z-50 transition-opacity duration-300 ${
        isVisible ? "opacity-100" : "opacity-0"
      }`}
    >
      <div
        className="h-full bg-gradient-to-r from-[#4D55F2] to-[#000cf4] transition-all duration-300 ease-out"
        style={{ width: `${progress}%` }}
      />
    </div>
  );
}
