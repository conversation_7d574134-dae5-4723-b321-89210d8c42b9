@import url("https://fonts.googleapis.com/css2?family=Rethink+Sans:ital,wght@0,400..800;1,400..800&display=swap");
@import "tailwindcss";

/* Prevent Samsung Internet and other browsers from applying force dark mode */
html {
  color-scheme: light only;
  forced-color-adjust: none;
}

/* Disable force dark mode in Samsung Internet and Chrome */
@media (prefers-color-scheme: dark) {
  html {
    color-scheme: light only;
  }
}

@theme {
  --color-primary-main: #343cd8;
  --font-RethinkSans: "Rethink Sans", "sans-serif";
}

body {
  font-family: "Rethink Sans", sans-serif;
  font-optical-sizing: auto;
  @apply: transition-all duration-300 ease-out;

  /* Explicitly set colors to prevent dark mode override */
  background-color: #ffffff !important;
  color: #000000 !important;

  /* Samsung Internet specific override */
  -webkit-forced-color-adjust: none;
  forced-color-adjust: none;
}

button {
  cursor: pointer;
}

@media only screen and (min-width: 450px) and (max-width: 650px) {
  .middleheroimg {
    @apply !translate-x-[-12%];
  }
}

.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}
