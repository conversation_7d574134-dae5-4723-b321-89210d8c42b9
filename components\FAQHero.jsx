import React, { useState } from "react";

// Mobile Navigation Sidebar Component
function MobileNavSidebar({ isOpen, onClose }) {
  const [showCompanyMenu, setShowCompanyMenu] = useState(false);

  if (!isOpen) return null;

  // Get current pathname to determine active state
  const currentPath =
    typeof window !== "undefined" ? window.location.pathname : "";

  const getActiveState = (path) => {
    if (path === "/" && currentPath === "/") return true;
    if (path === "/vendor" && currentPath === "/vendor") return true;
    if (path === "/marketplace" && currentPath === "/marketplace") return true;
    if (
      path === "company" &&
      !["/", "/vendor", "/marketplace"].includes(currentPath)
    )
      return true;
    return false;
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-40 backdrop-blur-sm bg-black/30 md:hidden"
        onClick={onClose}
      />

      {/* Sidebar */}
      <div className="fixed top-0 left-0 z-50 w-full h-full md:hidden">
        {/* Background with gradient matching the reference image */}
        <img
          src="/sidebar-illu.svg"
          alt=""
          className="top-0 translate-y-[50%] left-0 absolute"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-[#4D55F2] to-[#0109A5]">
          {/* Decorative background SVG */}

          {/* Semi-transparent white overlay with blur */}
          <div
            className="absolute inset-0"
            style={{
              background: "rgba(255, 255, 255, 0.08)",
              backdropFilter: "blur(24px)",
            }}
          />
        </div>

        {/* Content */}
        <div className="flex relative z-10 flex-col h-full">
          {/* Close button - top right */}
          <div className="flex justify-end p-6 pt-12">
            <button
              onClick={onClose}
              className="flex justify-center items-center w-9 h-9"
            >
              <svg width="36" height="36" viewBox="0 0 36 36" fill="none">
                <path
                  opacity="0.4"
                  d="M18 33C26.2843 33 33 26.2843 33 18C33 9.71573 26.2843 3 18 3C9.71573 3 3 9.71573 3 18C3 26.2843 9.71573 33 18 33Z"
                  fill="#B8BBFA"
                />
                <path
                  d="M19.5905 18L23.0405 14.55C23.4755 14.115 23.4755 13.395 23.0405 12.96C22.6055 12.525 21.8855 12.525 21.4505 12.96L18.0005 16.41L14.5505 12.96C14.1155 12.525 13.3955 12.525 12.9605 12.96C12.5255 13.395 12.5255 14.115 12.9605 14.55L16.4105 18L12.9605 21.45C12.5255 21.885 12.5255 22.605 12.9605 23.04C13.1855 23.265 13.4705 23.37 13.7555 23.37C14.0405 23.37 14.3255 23.265 14.5505 23.04L18.0005 19.59L21.4505 23.04C21.6755 23.265 21.9605 23.37 22.2455 23.37C22.5305 23.37 22.8155 23.265 23.0405 23.04C23.4755 22.605 23.4755 21.885 23.0405 21.45L19.5905 18Z"
                  fill="#B8BBFA"
                />
              </svg>
            </button>
          </div>

          {/* Logo - centered */}
          <div className="flex justify-center items-center px-6 mb-12">
            <div className="flex items-center">
              <img
                src="https://content.eventpark.africa/email/logo2.png"
                alt="EventPark"
                className="h-6"
              />
              <span className="text-white font-bold text-[24px] ml-2">
                EventPark.
              </span>
            </div>
          </div>

          {/* Navigation Items - centered */}
          <div className="flex-1 px-6">
            {!showCompanyMenu ? (
              <nav>
                <ul className="space-y-8 text-center">
                  <li>
                    <a
                      href="/"
                      className={` block py-2 px-6 rounded-full ${
                        getActiveState("/")
                          ? "font-bold bg-[white]/8 backdrop-blur-md text-[#B8BBFA] text-[40px]"
                          : "text-[24px] font-normal text-[#B8BBFA]"
                      }`}
                    >
                      Home
                    </a>
                  </li>
                  <li>
                    <button
                      onClick={() => setShowCompanyMenu(true)}
                      className={` block py-2 px-6 rounded-full w-full ${
                        getActiveState("company")
                          ? "font-bold text-[40px] bg-[white]/8 backdrop-blur-md text-[#B8BBFA]"
                          : "font-normal text-[24px] text-[#B8BBFA]"
                      }`}
                    >
                      Company
                    </button>
                  </li>
                  <li>
                    <a
                      href="/vendor"
                      className={` block py-2 px-6 rounded-full ${
                        getActiveState("/vendor")
                          ? "font-bold bg-[white]/8 backdrop-blur-md text-[#B8BBFA] text-[40px]"
                          : "text-[24px] font-normal text-[#B8BBFA]"
                      }`}
                    >
                      For Vendors
                    </a>
                  </li>
                  <li>
                    <a
                      href="/marketplace"
                      className={` block py-2 px-6 rounded-full ${
                        getActiveState("/marketplace")
                          ? "font-bold bg-[white]/8 backdrop-blur-md text-[#B8BBFA] text-[40px]"
                          : "text-[24px] font-normal text-[#B8BBFA]"
                      }`}
                    >
                      Marketplace
                    </a>
                  </li>
                </ul>
              </nav>
            ) : (
              /* Company Submenu */
              <div className="text-center bg-[white]/8 backdrop-blur-md my-6 rounded-md">
                <div className="mb-8">
                  <button
                    onClick={() => setShowCompanyMenu(false)}
                    className="text-white text-[40px] font-bold block py-2 px-6  text-[#B8BBFA] w-full"
                  >
                    Company
                  </button>
                </div>
                <nav>
                  <ul className="space-y-6">
                    <li>
                      <a
                        href="/about"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        About Us
                      </a>
                    </li>
                    <li>
                      <a
                        href="/contact"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Contact Us
                      </a>
                    </li>
                    <li>
                      <a
                        href="/privacy-policy"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Privacy Policy
                      </a>
                    </li>
                    <li>
                      <a
                        href="/terms-of-service"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Terms of Use
                      </a>
                    </li>
                    <li>
                      <a
                        href="/career"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Careers
                      </a>
                    </li>
                    <li>
                      <a
                        href="/blog"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Blog
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            )}
          </div>

          {/* Account Buttons */}
          <div className="px-6 pb-12 space-y-4">
            <button className="w-full py-4 px-6 bg-[#9CC1FC] text-black rounded-full font-medium text-[18px]">
              Create an Account
            </button>
            <button className="w-full py-4 px-6 bg-[#4D55F2] text-white rounded-full font-medium text-[18px]">
              Sign in to your account
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

export function FAQHero() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <div className="overflow-hidden h-[500px] md:h-[700px] relative w-full bg-white after:content-[''] after:absolute after:bottom-0 after:bg-[url('/faqrectangle.svg')] after:w-full after:h-[80px] after:bg-no-repeat after:bg-top after:bg-cover">
      {/* Background SVG - using the faqhero.svg from public directory */}
      <div
        className="absolute inset-0 bg-center bg-top bg-no-repeat bg-cover"
        style={{
          backgroundImage:
            "linear-gradient(rgba(235, 243, 254, 0.25)), url('/faqhero.jpg')",
        }}
      ></div>

      {/* Mobile Navigation Sidebar */}
      <MobileNavSidebar
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />

      {/* Content */}
      <div className="container relative z-10 px-4 py-8 mx-auto max-w-7xl">
        {/* Header with logo and mobile controls */}
        <div className="flex relative justify-center items-center mb-8">
          {/* Mobile Hamburger Menu - Left side */}

          {/* Logo - Center */}
          <div className="flex items-center">
            <img src="/logo.svg" alt="EventPark" className="h-6" />
            <span className="text-[#4D55F2] font-bold text-[24px] ml-2">
              EventPark.
            </span>
          </div>

          <button
            onClick={() => setIsMobileMenuOpen(true)}
            className="flex absolute right-0 justify-center items-center w-10 h-10 md:hidden"
          >
            <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
              <circle cx="20" cy="20" r="20" fill="#FEF5F1" fillOpacity="0.8" />
              <rect
                x="16.5"
                y="24.8999"
                width="11.6667"
                height="1"
                fill="#7D665C"
              />
              <rect
                x="11.8335"
                y="19.0667"
                width="16.3333"
                height="1"
                fill="#7D665C"
              />
              <rect
                x="16.5"
                y="13.2334"
                width="11.6667"
                height="1"
                fill="#7D665C"
              />
            </svg>
          </button>
        </div>

        {/* FAQ Title */}
        <div className="mt-[100px] mb-4 text-center">
          <p className="mb-2 text-[18px] tracking-wider text-gray-500 uppercase">
            FAQs
          </p>
          <h1 className="md:text-[100px] text-[55px] mt-[10px] md:tracking-[-6px] font-bold leading-tight">
            <span className="text-[#4D2C1B]">Got </span>
            <span className="text-[#C9A99D]">Questions?</span>
          </h1>
          <p className="mx-auto mt-4 max-w-lg text-[20px] md:text-[28px]   text-[#8E8E93]">
            We answered some questions so you don't have to ask again
          </p>
        </div>
      </div>
    </div>
  );
}
