.hero-swiper-container {
  width: 100%;
  padding: 0;
  margin: 0 auto;
  overflow: unset;
}

.hero-swiper-container2 {
  width: 100%;
  padding: 0;
  margin: 0 auto;
  overflow: unset;
  @apply container;
}

.mySwiper {
  width: 100%;
  overflow: clip !important;
  padding: 20px 0;
}

/* Make the carousel full-width on all screen sizes */
@media (min-width: 768px) {
  .hero-swiper-container .swiper {
    width: calc(100vw - 3%);

    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
  }

  .hero-swiper-container2 .swiper {
    width: 750px;
    margin-inline: auto;
    left: 50%;
    right: 50%;
    transform: translateX(30%);
    margin-left: -50vw;
    margin-right: -50vw;
    position: relative;
  }
}

@media (min-width: 1440px) {
  .hero-swiper-container .swiper {
    width: calc(100vw - 3%);

    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
  }

  .hero-swiper-container2 .swiper {
    width: 750px;
    margin-inline: auto;
    left: 50%;
    right: 50%;
    transform: translateX(50%);
    margin-left: -50vw;
    margin-right: -50vw;
    position: relative;
  }
}

@media (min-width: 1600px) {
  .hero-swiper-container .swiper {
    width: calc(100vw - 3%);

    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
  }

  .hero-swiper-container2 .swiper {
    width: 750px;
    margin-inline: auto;
    left: 50%;
    right: 50%;
    transform: translateX(80%);
    margin-left: -50vw;
    margin-right: -50vw;
    position: relative;
  }
}

/* Base styles for all slides */
.swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.5s ease;
  transform: scale(0.7);
  opacity: 0.7;
}

.swiper-slide img {
  width: 100%;
  height: auto;
  aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: 24px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Active slide (center) */
.swiper-slide-active {
  opacity: 1;
  transform: scale(1);
  z-index: 10;
}

.swiper-slide-active img {
  width: 100%;
  height: auto;
}

/* First level adjacent slides */
.swiper-slide-prev,
.swiper-slide-next {
  opacity: 0.9;
  transform: scale(0.85);
  z-index: 5;
}

/* Second level adjacent slides */
.swiper-slide-prev + .swiper-slide-prev,
.swiper-slide-next + .swiper-slide-next {
  opacity: 0.7;
  transform: scale(0.7);
  z-index: 3;
}

/* Keep all slides at 100% width to ensure 5 are visible */
.swiper-wrapper {
  align-items: center;
}

/* Mobile adjustments - show 3 slides on smaller screens */
@media (max-width: 640px) {
  .swiper-slide-active {
    transform: scale(1.05);
  }

  .hero-swiper-container {
    padding: 0 5px;
  }

  .hero-swiper-container2 {
    padding: 0 5px;
  }
}

/* Ensure proper looping */
.swiper-wrapper {
  transition-timing-function: linear !important;
}
