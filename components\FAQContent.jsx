import React, { useState } from "react";
import WaitlistModal from "./WaitlistModal";

export function FAQContent() {
  const [showWaitlistModal, setShowWaitlistModal] = useState(false);
  const handleOpenWaitlist = (e) => {
    e.preventDefault();
    setShowWaitlistModal(true);
  };

  const handleCloseWaitlist = () => {
    setShowWaitlistModal(false);
  };
  // State for active tab
  const [activeTab, setActiveTab] = useState("planners");

  // State to track which FAQ item is open
  const [openIndex, setOpenIndex] = useState(1);

  // Toggle function for FAQ items
  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? -1 : index);
  };

  // FAQ data for event planners
  const plannerFAQs = [
    {
      question: "What is EventPark?",
      answer:
        "EventPark is an event management platform that streamlines the process of finding, booking, and managing vendors for events.",
    },
    {
      question: "How do I book a vendor through EventPark?",
      answer:
        "You can browse our marketplace, filter vendors by category, location, and price range, then request a booking directly through our platform. Our system handles all the communication and payment processing.",
    },
    {
      question: "How do I search for vendors on EventPark?",
      answer:
        "Use our search function and filters to find vendors based on service type, location, price range, availability, and ratings. You can also save your favorite vendors for future reference.",
    },
    {
      question: "Can I filter vendors by budget, location or service rendered?",
      answer:
        "Yes, you can filter vendors by budget range, location, service type, availability, and ratings to find the perfect match for your event needs.",
    },
    {
      question: "How do I book a vendor through EventPark?",
      answer:
        "Once you've found a vendor you like, you can request a booking through their profile page. You'll need to provide event details, and the vendor will respond with availability and pricing.",
    },
    {
      question: "Can I pay vendors directly on EventPark?",
      answer:
        "Yes, EventPark provides a secure payment system that allows you to pay vendors directly through the platform. This ensures both parties are protected throughout the transaction.",
    },
    {
      question: "What happens if I need to cancel an event?",
      answer:
        "If you need to cancel an event, you can do so through your dashboard. Refund policies vary by vendor, and these are clearly stated before you make a booking.",
    },
    {
      question: "Is there a booking fee when using EventPark?",
      answer:
        "EventPark charges a small service fee on bookings to maintain the platform and provide customer support. This fee is clearly displayed before you complete your booking.",
    },
    {
      question: "Can I talk directly to the vendor?",
      answer:
        "Yes, once you've initiated a booking request, you can communicate directly with the vendor through our messaging system to discuss details and requirements.",
    },
  ];

  // FAQ data for vendors
  const vendorFAQs = [
    {
      question: "How do I create a profile on EventPark?",
      answer:
        "EventPark is an event management platform that streamlines the process of finding, booking, and managing vendors for events.",
    },
    {
      question: "What information should I include in my vendor profile?",
      answer:
        "Your profile should include your business name, description, services offered, pricing information, portfolio images, availability calendar, and contact details. The more complete your profile, the more likely you are to attract clients.",
    },
    {
      question: "Can I set my own pricing for services offered?",
      answer:
        "Yes, you have complete control over your pricing. You can set fixed prices, price ranges, or custom quotes based on client requirements. You can also offer special packages or discounts for different seasons or events.",
    },
    {
      question: "Are there any fees for using EventPark?",
      answer:
        "EventPark charges a small commission on bookings made through our platform. The exact percentage depends on your service category and pricing tier. There are no monthly fees or setup costs to join as a vendor.",
    },
    {
      question: "What is the payment process for vendor bookings?",
      answer:
        "When a client books your services, the payment is held in escrow until the event is completed. Once the client confirms that the services were delivered satisfactorily, the funds are released to your account within 3-5 business days.",
    },
    {
      question: "How do I manage multiple bookings for different events?",
      answer:
        "Our vendor dashboard provides a comprehensive calendar view where you can see all your bookings, pending requests, and blocked dates. You can manage multiple events simultaneously and set buffer times between bookings if needed.",
    },
    {
      question: "Can I set my location preferences and target specific areas?",
      answer:
        "Yes, you can specify your service area by setting a radius from your location or selecting specific cities or regions. This helps match you with clients in your preferred service areas and manage travel expectations.",
    },
    {
      question: "How can I receive reviews and ratings from event planners?",
      answer:
        "After completing an event, clients are prompted to leave reviews and ratings. These are displayed on your profile and help build your reputation on the platform. You can also respond to reviews to show your commitment to client satisfaction.",
    },
    {
      question: "How do I close or deactivate my vendor account if needed?",
      answer:
        "You can temporarily deactivate your account from your vendor dashboard if you need a break, or contact our support team to permanently close your account. Note that you'll need to fulfill any existing bookings before closing your account.",
    },
  ];

  // Get the active FAQ list based on the selected tab
  const activeFAQs = activeTab === "planners" ? plannerFAQs : vendorFAQs;

  return (
    <div className="w-full bg-white">
      {showWaitlistModal && <WaitlistModal onClose={handleCloseWaitlist} />}
      <div className="mx-auto w-full">
        {/* Navigation */}
        <div className="mt-8 mb-16">
          <nav className="flex justify-center">
            <ul className="flex px-8 py-3 space-x-12 bg-white rounded-full shadow-lg">
              <li>
                <a
                  href="/"
                  className="text-[10px] sm:text-[12px] md:text-sm font-medium text-black hover:text-[#4D55F2]"
                >
                  HOME
                </a>
              </li>

              <li>
                <a
                  href="/vendor"
                  className="text-[10px] sm:text-[12px] md:text-sm font-medium text-black hover:text-[#4D55F2]"
                >
                  FOR VENDORS
                </a>
              </li>
              <li>
                <a
                  href="/marketplace"
                  className="text-[10px] sm:text-[12px] md:text-sm font-medium text-black hover:text-[#4D55F2]"
                >
                  MARKETPLACE
                </a>
              </li>
            </ul>
          </nav>
        </div>

        {/* FAQ Tabs */}
        <div className="flex justify-center px-4 mb-12">
          <div className="flex gap-8 mb-[28px] bg-[#F2F2F7] p-2 rounded-full">
            <div className="flex relative flex-col items-center">
              <div
                onClick={() => setActiveTab("planners")}
                className={`flex h-[36px] justify-center items-center ${
                  activeTab === "planners" ? "bg-white" : ""
                } px-[9px] transition-all duration-500 ease-out rounded-full w-[140px] py-4 cursor-pointer`}
              >
                <span
                  className={`text-[14px] tracking-[-2%] transition-all duration-500 whitespace-nowrap ease-out ${
                    activeTab === "planners"
                      ? "text-[#4D55F2] font-bold"
                      : "text-[#666666]"
                  }`}
                >
                  For Event Planners
                </span>
              </div>
              {activeTab === "planners" && (
                <div className="min-h-[4px] absolute transition-all duration-500 ease-out bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
              )}
            </div>

            <div className="flex relative flex-col items-center">
              <div
                className={`flex h-[36px] justify-center items-center ${
                  activeTab === "vendors" ? "bg-white" : ""
                } px-[9px] rounded-full w-[108px] py-4 transition-all duration-500 ease-out cursor-pointer`}
                onClick={() => setActiveTab("vendors")}
              >
                <span
                  className={`text-[14px] tracking-[-2%] transition-all duration-500 ease-out ${
                    activeTab === "vendors"
                      ? "text-[#4D55F2] font-bold"
                      : "text-[#666666]"
                  }`}
                >
                  For Vendors
                </span>
              </div>
              {activeTab === "vendors" && (
                <div className="min-h-[4px] absolute bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
              )}
            </div>
          </div>
        </div>

        {/* FAQ Items */}
        <div className="px-4 mx-auto max-w-3xl">
          {activeFAQs.map((faq, index) => (
            <div
              key={index}
              className={`py-6 border-b border-gray-200 ${
                index === activeFAQs.length - 1 ? "border-b-0" : ""
              }`}
            >
              <button
                className="flex justify-between items-center w-full text-left focus:outline-none"
                onClick={() => toggleFAQ(index)}
              >
                <h3
                  className={`${
                    openIndex === index
                      ? "text-[#0109A5] font-medium"
                      : "text-[#444547] font-normal"
                  } text-lg`}
                >
                  {faq.question}
                </h3>
                <span className="text-gray-400">
                  {openIndex === index ? (
                    <svg
                      width="16"
                      height="2"
                      viewBox="0 0 16 2"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 1H15"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                      />
                    </svg>
                  ) : (
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M8 1V15M1 8H15"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                      />
                    </svg>
                  )}
                </span>
              </button>
              <div
                className={`overflow-hidden transition-all duration-300 ${
                  openIndex === index ? "max-h-96 mt-4" : "max-h-0"
                }`}
              >
                <p className="text-[#666666] leading-relaxed">{faq.answer}</p>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="overflow-hidden relative mt-20">
          <div className="overflow-hidden relative h-[500px]">
            {/* CTA Tab Switcher */}
            <div className="flex absolute right-0 left-0 top-8 z-20 justify-center">
              <div className="flex p-1 text-white rounded-full backdrop-blur-[24px] bg-black/16">
                <button
                  className={`px-4 py-2 text-sm font-medium rounded-full transition-all ${
                    activeTab === "planners"
                      ? "bg-white text-black"
                      : "text-white"
                  }`}
                  onClick={() => setActiveTab("planners")}
                >
                  Event Planners
                </button>
                <button
                  className={`px-4 py-2 text-sm font-medium rounded-full transition-all ${
                    activeTab === "vendors"
                      ? "bg-white text-black"
                      : "text-white"
                  }`}
                  onClick={() => setActiveTab("vendors")}
                >
                  For Vendors
                </button>
              </div>
            </div>

            {/* Background images based on active tab */}
            <div className="relative h-full">
              {activeTab === "planners" ? (
                <div className="relative h-full">
                  <img
                    src="/faqcta.jpg"
                    alt="Event planning"
                    className="object-cover w-full h-full"
                  />
                  {/* Dark overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-[#00000D] to-[#000059] opacity-50"></div>
                </div>
              ) : (
                <div className="relative h-full">
                  <img
                    src="/faqcta2.jpg"
                    alt="Vendor services"
                    className="object-cover w-full h-full"
                  />
                  {/* Dark overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-[#00000D] to-[#000059] opacity-50"></div>
                </div>
              )}
            </div>

            {/* Content based on active tab */}
            <div className="flex absolute inset-0 z-10 justify-center items-center">
              <div className="px-8 py-16 mt-8 text-center text-white">
                {activeTab === "planners" ? (
                  <>
                    <h2 className="mb-4 text-3xl font-bold md:text-4xl">
                      Ready to Plan Your
                      <div className="mt-2 text-4xl md:text-5xl">
                        Next Event?
                      </div>
                    </h2>
                    <p className="mb-8 text-gray-300 max-w-[350px]">
                      Join thousands of event planners who are creating
                      memorable experiences with EventPark
                    </p>
                  </>
                ) : (
                  <>
                    <h2 className="mb-4 text-3xl font-bold md:text-4xl">
                      Ready to Grow
                      <div className="mt-2 text-4xl md:text-5xl">
                        Your Business
                      </div>
                    </h2>
                    <p className="mb-8 text-gray-300 max-w-[350px]">
                      Join our network of vendors and reach more clients for
                      your event services
                    </p>
                  </>
                )}
                <button
                  onClick={handleOpenWaitlist}
                  className="bg-white text-[#000000] font-medium py-3 px-6 rounded-full hover:bg-gray-100 transition-colors"
                >
                  Get Started Now!
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
