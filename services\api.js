import axios from 'axios';

// Base URL from environment variable or fallback to the provided URL
const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://api-dev.eventpark.africa';

// Create axios instance with default config
const axiosInstance = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types for TypeScript (these would be in a .ts file in a TypeScript project)
/**
 * @typedef {Object} WaitlistFilters
 * @property {string} [email] - Email filter
 * @property {string} [joining_as] - Role filter
 */

/**
 * @typedef {Object} WaitlistResponse
 * @property {boolean} success - Whether the request was successful
 * @property {string} message - Response message
 * @property {Object} data - Response data
 */

/**
 * @typedef {Object} WaitlistRequest
 * @property {string} email - Email address
 * @property {string} [first_name] - First name
 * @property {string} [last_name] - Last name
 * @property {string} joining_as - Role (user, vendor, etc.)
 */

// Waitlist API functions
export const waitlistApi = {
  /**
   * Get waitlist entries with optional filters
   * @param {WaitlistFilters} [filters] - Optional filters
   * @returns {Promise<WaitlistResponse>} - Waitlist response
   */
  getWaitlist: async (filters) => {
    const response = await axiosInstance.get(`/v1/waitlist`, {
      params: filters,
    });
    return response.data;
  },

  /**
   * Add a new entry to the waitlist
   * @param {WaitlistRequest} data - Waitlist request data
   * @returns {Promise<WaitlistResponse>} - Waitlist response
   */
  addToWaitlist: async (data) => {
    const response = await axiosInstance.post(`/v1/waitlist`, data);
    return response.data;
  },
};

export default axiosInstance;
