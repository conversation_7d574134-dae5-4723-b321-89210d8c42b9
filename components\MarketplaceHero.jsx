import React, { useState } from "react";
// Import custom navigation hook
import { useClientNavigation } from "../renderer/useClientNavigation";

export function MarketplaceHero() {
  const [activeTab, setActiveTab] = useState("marketplace");
  const { navigate } = useClientNavigation();

  return (
    <>
      <div className="bg-gradient-to-b from-[#FEF7F4] to-[#EBF3FE] w-full relative overflow-hidden">
        {/* Background illustration */}

        <div className="container py-12 pt-[60px] mx-auto max-w-7xl relative z-10">
          <div className="flex flex-col items-center text-center">
            {/* Logo and Navigation */}
            <div className="flex justify-center px-4 mb-8 w-full">
              <div className="flex flex-col items-center">
                <div className="text-[#000073] font-bold text-[24px] tracking-[-0.01px] mb-6">
                  <div>
                    <span className="inline-flex relative gap-1 item">
                      <img src="/logo.svg" alt="" />
                      EventPark
                      <span className="min-w-[4px] w-[4px] h-[4px] min-h-[4px] rounded-full bg-[#FF5519] absolute bottom-[10px] right-[-8px]"></span>
                    </span>
                  </div>
                </div>

                {/* Navigation Tabs */}
                <div className="flex gap-8 mb-[28px] bg-[#FDEFE9] p-2 rounded-full">
                  <div className="flex relative flex-col items-center">
                    <div
                      onClick={() => {
                        setActiveTab("forYou");
                        navigate("/");
                      }}
                      className={`flex h-[36px] justify-center items-center ${
                        activeTab === "forYou" ? "bg-white" : ""
                      } px-[9px] transition-all duration-500 ease-out rounded-full w-[77px] py-4 cursor-pointer`}
                    >
                      <span
                        className={`text-[14px] tracking-[-2%] transition-all duration-500 ease-out ${
                          activeTab === "forYou"
                            ? "text-[#FF6630] font-bold"
                            : "text-[#666666]"
                        }`}
                      >
                        For You
                      </span>
                    </div>
                    {activeTab === "forYou" && (
                      <div className="min-h-[4px] absolute transition-all duration-500 ease-out bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
                    )}
                  </div>

                  <div className="flex relative flex-col items-center">
                    <div
                      onClick={() => {
                        setActiveTab("forVendors");
                        navigate("/vendor");
                      }}
                      className={`flex h-[36px] justify-center items-center ${
                        activeTab === "forVendors" ? "bg-white" : ""
                      } px-[9px] rounded-full w-[108px] py-4 transition-all duration-500 ease-out cursor-pointer`}
                    >
                      <span
                        className={`text-[14px] tracking-[-2%] transition-all duration-500 ease-out ${
                          activeTab === "forVendors"
                            ? "text-[#FF6630] font-bold"
                            : "text-[#666666]"
                        }`}
                      >
                        For Vendors
                      </span>
                    </div>
                    {activeTab === "forVendors" && (
                      <div className="min-h-[4px] absolute bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
                    )}
                  </div>

                  <div className="flex relative flex-col items-center">
                    <div
                      className={`flex h-[36px] justify-center items-center ${
                        activeTab === "marketplace" ? "bg-white" : ""
                      } px-[9px] rounded-full w-[108px] py-4 transition-all duration-500 ease-out cursor-pointer`}
                    >
                      <span
                        className={`text-[14px] tracking-[-2%] transition-all duration-500 ease-out ${
                          activeTab === "marketplace"
                            ? "text-[#FF6630] font-bold"
                            : "text-[#666666]"
                        }`}
                      >
                        Marketplace
                      </span>
                    </div>
                    {activeTab === "marketplace" && (
                      <div className="min-h-[4px] absolute bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Hero Content */}
            <div className="flex flex-col items-center px-4 mb-16">
              <h1 className="mb-6 text-center">
                <span className="block text-[#FF9975] text-[40px] md:text-[56px] font-bold leading-[100%] tracking-tight">
                  Find. Book
                </span>
                <span className="block text-[#000073] text-[56px] md:text-[80px] font-bold leading-[100%] tracking-tight">
                  Celebrate
                </span>
              </h1>
              <div className="relative">
                <div className="absolute inset-0 w-[420px] h-[400px] z-[9]">
                  <img
                    src="/marketplace-illustration.svg"
                    alt=""
                    className="object-cover w-full h-full"
                    onError={(e) => {
                      // Hide if image doesn't exist
                      e.target.style.display = "none";
                    }}
                  />
                </div>
                <p className="mb-8 text-[#666666] text-[16px] md:text-[18px] text-center max-w-[336px] relative z-[10]">
                  Explore 200+ categories and connect with the best in the
                  business
                </p>

                {/* Search Bar */}
                <div className="flex w-full max-w-[336px] mb-8 bg-white rounded-full overflow-hidden shadow-sm relative z-[10]">
                  <input
                    type="text"
                    placeholder="Search marketplace"
                    className="flex-grow px-6 py-3 text-[#666666] focus:outline-none"
                  />
                  <div className="flex items-center pr-4 bg-white">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z"
                        stroke="#CCCCCC"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M21 21L16.65 16.65"
                        stroke="#CCCCCC"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
