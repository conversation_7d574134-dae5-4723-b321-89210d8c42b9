// pages/notfound.page.jsx
import React from "react";

export { Page };

function Page() {
  return (
    <div className="min-h-screen bg-[#FAFAFA] flex items-center justify-center p-4">
      <div className="w-full max-w-2xl text-center">
        {/* Logo */}
        <div className="flex gap-3 justify-center items-center mb-8">
          <img src="/logo2.svg" alt="EventPark logo" className="h-8" />
          <div className="text-[#000073] tracking-[-3%] text-[24px] font-bold leading-[100%]">
            EventPark
            <span className="text-[#FF5519] tracking-[-3%] text-[24px] font-bold leading-[100%]">
              .
            </span>
          </div>
        </div>

        {/* 404 Image */}
        <div className="mb-8">
          <img
            src="/404-illustration.svg"
            alt="404 Illustration"
            className="mx-auto max-w-md"
            onError={(e) => {
              // Fallback if image doesn't exist
              const target = e.target;
              target.style.display = "none";
            }}
          />
        </div>

        {/* Error Message */}
        <h1 className="mb-4 text-4xl font-bold text-gray-900">
          Page Not Found
        </h1>
        <p className="mx-auto mb-8 max-w-md text-gray-600">
          We couldn't find the page you're looking for. Please check the URL or
          return to the dashboard.
        </p>

        {/* Back Button */}
        <a
          href="/"
          className="inline-flex items-center gap-2 px-6 py-3 bg-[#343CD8] text-white rounded-md hover:bg-[#2930B3] transition-colors"
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M19 12H5M5 12L12 19M5 12L12 5" />
          </svg>
          Back to Dashboard
        </a>
      </div>
    </div>
  );
}
