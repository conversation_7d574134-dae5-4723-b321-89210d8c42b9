import React, { useState } from "react";
import { Footer } from "./Footer";

// Mobile Navigation Sidebar Component (copied from FAQHero)
function MobileNavSidebar({ isOpen, onClose }) {
  const [showCompanyMenu, setShowCompanyMenu] = useState(false);

  if (!isOpen) return null;

  // Get current pathname to determine active state
  const currentPath =
    typeof window !== "undefined" ? window.location.pathname : "";

  const getActiveState = (path) => {
    if (path === "/" && currentPath === "/") return true;
    if (path === "/vendor" && currentPath === "/vendor") return true;
    if (path === "/marketplace" && currentPath === "/marketplace") return true;
    if (
      path === "company" &&
      !["/", "/vendor", "/marketplace"].includes(currentPath)
    )
      return true;
    return false;
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-40 backdrop-blur-sm bg-black/30 md:hidden"
        onClick={onClose}
      />

      {/* Sidebar */}
      <div className="fixed top-0 left-0 z-50 w-full h-full md:hidden">
        {/* Background with gradient matching the reference image */}
        <img
          src="/sidebar-illu.svg"
          alt=""
          className="top-0 translate-y-[50%] left-0 absolute"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-[#4D55F2] to-[#0109A5]">
          {/* Decorative background SVG */}

          {/* Semi-transparent white overlay with blur */}
          <div
            className="absolute inset-0"
            style={{
              background: "rgba(255, 255, 255, 0.08)",
              backdropFilter: "blur(24px)",
            }}
          />
        </div>

        {/* Content */}
        <div className="flex relative z-10 flex-col h-full">
          {/* Close button - top right */}
          <div className="flex justify-end p-6 pt-12">
            <button
              onClick={onClose}
              className="flex justify-center items-center w-9 h-9"
            >
              <svg width="36" height="36" viewBox="0 0 36 36" fill="none">
                <path
                  opacity="0.4"
                  d="M18 33C26.2843 33 33 26.2843 33 18C33 9.71573 26.2843 3 18 3C9.71573 3 3 9.71573 3 18C3 26.2843 9.71573 33 18 33Z"
                  fill="#B8BBFA"
                />
                <path
                  d="M19.5905 18L23.0405 14.55C23.4755 14.115 23.4755 13.395 23.0405 12.96C22.6055 12.525 21.8855 12.525 21.4505 12.96L18.0005 16.41L14.5505 12.96C14.1155 12.525 13.3955 12.525 12.9605 12.96C12.5255 13.395 12.5255 14.115 12.9605 14.55L16.4105 18L12.9605 21.45C12.5255 21.885 12.5255 22.605 12.9605 23.04C13.1855 23.265 13.4705 23.37 13.7555 23.37C14.0405 23.37 14.3255 23.265 14.5505 23.04L18.0005 19.59L21.4505 23.04C21.6755 23.265 21.9605 23.37 22.2455 23.37C22.5305 23.37 22.8155 23.265 23.0405 23.04C23.4755 22.605 23.4755 21.885 23.0405 21.45L19.5905 18Z"
                  fill="#B8BBFA"
                />
              </svg>
            </button>
          </div>

          {/* Logo - centered */}
          <div className="flex justify-center items-center px-6 mb-12">
            <div className="flex items-center">
              <img
                src="https://content.eventpark.africa/email/logo2.png"
                alt="EventPark"
                className="h-6"
              />
              <span className="text-white font-bold text-[24px] ml-2">
                EventPark.
              </span>
            </div>
          </div>

          {/* Navigation Items - centered */}
          <div className="flex-1 px-6">
            {!showCompanyMenu ? (
              <nav>
                <ul className="space-y-8 text-center">
                  <li>
                    <a
                      href="/"
                      className={` block py-2 px-6 rounded-full ${
                        getActiveState("/")
                          ? "font-bold bg-[white]/8 backdrop-blur-md text-[#B8BBFA] text-[40px]"
                          : "text-[24px] font-normal text-[#B8BBFA]"
                      }`}
                    >
                      Home
                    </a>
                  </li>
                  <li>
                    <button
                      onClick={() => setShowCompanyMenu(true)}
                      className={` block py-2 px-6 rounded-full w-full ${
                        getActiveState("company")
                          ? "font-bold text-[40px] bg-[white]/8 backdrop-blur-md text-[#B8BBFA]"
                          : "font-normal text-[24px] text-[#B8BBFA]"
                      }`}
                    >
                      Company
                    </button>
                  </li>
                  <li>
                    <a
                      href="/vendor"
                      className={` block py-2 px-6 rounded-full ${
                        getActiveState("/vendor")
                          ? "font-bold bg-[white]/8 backdrop-blur-md text-[#B8BBFA] text-[40px]"
                          : "text-[24px] font-normal text-[#B8BBFA]"
                      }`}
                    >
                      For Vendors
                    </a>
                  </li>
                  <li>
                    <a
                      href="/marketplace"
                      className={` block py-2 px-6 rounded-full ${
                        getActiveState("/marketplace")
                          ? "font-bold bg-[white]/8 backdrop-blur-md text-[#B8BBFA] text-[40px]"
                          : "text-[24px] font-normal text-[#B8BBFA]"
                      }`}
                    >
                      Marketplace
                    </a>
                  </li>
                </ul>
              </nav>
            ) : (
              /* Company Submenu */
              <div className="text-center bg-[white]/8 backdrop-blur-md my-6 rounded-md">
                <div className="mb-8">
                  <button
                    onClick={() => setShowCompanyMenu(false)}
                    className="text-white text-[40px] font-bold block py-2 px-6  text-[#B8BBFA] w-full"
                  >
                    Company
                  </button>
                </div>
                <nav>
                  <ul className="space-y-6">
                    <li>
                      <a
                        href="/about"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        About Us
                      </a>
                    </li>
                    <li>
                      <a
                        href="/contact"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Contact Us
                      </a>
                    </li>
                    <li>
                      <a
                        href="/privacy-policy"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Privacy Policy
                      </a>
                    </li>
                    <li>
                      <a
                        href="/terms-of-service"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Terms of Use
                      </a>
                    </li>
                    <li>
                      <a
                        href="/career"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Careers
                      </a>
                    </li>
                    <li>
                      <a
                        href="/blog"
                        className="text-[#B8BBFA] text-[24px] font-normal block py-2"
                      >
                        Blog
                      </a>
                    </li>
                  </ul>
                </nav>
              </div>
            )}
          </div>

          {/* Account Buttons */}
          <div className="px-6 pb-12 space-y-4">
            <button className="w-full py-4 px-6 bg-[#9CC1FC] text-black rounded-full font-medium text-[18px]">
              Create an Account
            </button>
            <button className="w-full py-4 px-6 bg-[#4D55F2] text-white rounded-full font-medium text-[18px]">
              Sign in to your account
            </button>
          </div>
        </div>
      </div>
    </>
  );
}

export function ContactUsContent() {
  const [openIndex, setOpenIndex] = useState(0);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    message: "",
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission logic here
    console.log("Form submitted:", formData);
    // Reset form
    setFormData({
      fullName: "",
      email: "",
      message: "",
    });
  };

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? -1 : index);
  };

  // FAQ data
  const faqItems = [
    {
      question: "What is EventPark?",
      answer:
        "EventPark is an event management platform that streamlines the process of finding, booking, and managing vendors for events.",
    },
    {
      question: "How do I search for vendors on EventPark?",
      answer:
        "You can search for vendors by category, location, price range, or specific services offered. Our advanced search filters help you find the perfect match for your event needs.",
    },
    {
      question:
        "Can I filter vendors by budget, location, or services offered?",
      answer:
        "Yes, EventPark offers comprehensive filtering options. You can filter vendors by budget range, geographic location, specific services, availability dates, and customer ratings to find the perfect match for your event requirements.",
    },
    {
      question: "How do I book a vendor through EventPark?",
      answer:
        "After finding a vendor you like, you can view their profile, check availability, and send a booking request. Once the vendor accepts, you can finalize the details and make secure payments through our platform.",
    },
    {
      question: "Can I issue vendors to order back to plan?",
      answer:
        "Yes, EventPark allows you to communicate directly with vendors and make adjustments to your orders. You can request changes, additions, or modifications to your existing bookings through our platform.",
    },
  ];

  return (
    <div className="w-full">
      {/* Mobile Navigation Sidebar */}
      <MobileNavSidebar
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
      />

      {/* Hero Section */}
      <div
        className="relative w-full h-[950px] flex flex-col items-center justify-center overflow-hidden "
        style={{
          backgroundImage:
            "linear-gradient(to bottom, rgba(0, 0, 0, 0.2) 0%, rgb(0, 0, 0) 100%, rgba(0, 0, 0, 0.4) 12%),  url('/contact-hero.jpg')",
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundBlendMode: "multiply",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="absolute bottom-0">
          <img
            src="/contact-rectangle.svg"
            className="w-full h-[80px] object-cover"
            alt=""
          />
        </div>
        <div className="flex absolute top-8 justify-center items-center w-full">
          <div className="container flex relative justify-center items-center px-4">
            {/* Logo - Center */}
            <div className="flex items-center">
              <img src="/logo.svg" alt="EventPark" className="h-6" />
              <span className="text-[#4D55F2] font-bold text-[24px] ml-2">
                EventPark.
              </span>
            </div>

            {/* Mobile Hamburger Menu - Right side */}
            <button
              onClick={() => setIsMobileMenuOpen(true)}
              className="flex absolute right-0 justify-center items-center w-10 h-10 md:hidden"
            >
              <svg width="40" height="40" viewBox="0 0 40 40" fill="none">
                <circle
                  cx="20"
                  cy="20"
                  r="20"
                  fill="#FEF5F1"
                  fillOpacity="0.8"
                />
                <rect
                  x="16.5"
                  y="24.8999"
                  width="11.6667"
                  height="1"
                  fill="#7D665C"
                />
                <rect
                  x="11.8335"
                  y="19.0667"
                  width="16.3333"
                  height="1"
                  fill="#7D665C"
                />
                <rect
                  x="16.5"
                  y="13.2334"
                  width="11.6667"
                  height="1"
                  fill="#7D665C"
                />
              </svg>
            </button>
          </div>
        </div>

        <div className="z-10 mt-8 text-center">
          <p className="mb-2 text-sm font-medium tracking-wider text-white uppercase">
            CONTACT US
          </p>
          <h1 className="mb-4 text-[48px] md:text-[130px] md:tracking-[-8px] font-bold md:text-8xl">
            <span className="text-white">Get in </span>
            <span className="text-white/70">Touch</span>
          </h1>
          <p className="mb-8 text-lg font-light text-white/80">
            We are never far from you.
          </p>

          <div className="flex gap-2 justify-center p-5 mx-auto mt-4 rounded-full backdrop-blur-xl bg-white/4 w-fit">
            {/* <div className="flex justify-center items-center w-10 h-10 rounded-full backdrop-blur-xl bg-white/10">
              <svg
                width="21"
                height="20"
                viewBox="0 0 21 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  opacity="0.4"
                  d="M13.9915 1.66675H7.01647C3.98314 1.66675 2.1748 3.47508 2.1748 6.50841V13.4834C2.1748 16.5167 3.98314 18.3251 7.01647 18.3251H13.9915C17.0248 18.3251 18.8331 16.5167 18.8331 13.4834V6.50841C18.8331 3.47508 17.0248 1.66675 13.9915 1.66675Z"
                  fill="#EDEEFE"
                />
                <path
                  d="M12.1006 7.73325L12.1422 10.1916L14.3339 10.1583C14.4922 10.1583 14.6089 10.2999 14.5839 10.4499L14.2922 12.0416C14.2672 12.1583 14.1672 12.2416 14.0506 12.2499L12.1756 12.2833L12.2755 18.3249L9.77555 18.3666L9.67555 12.3249L8.25888 12.3499C8.11721 12.3499 8.00889 12.2416 8.00889 12.0999L7.98389 10.5166C7.98389 10.3749 8.09221 10.2666 8.23388 10.2666L9.65055 10.2416L9.60887 7.53326C9.58387 6.14992 10.6839 5.01659 12.0672 4.99159L14.3172 4.95825C14.4589 4.95825 14.5672 5.06659 14.5672 5.20825L14.6006 7.20826C14.6006 7.34992 14.4922 7.45825 14.3505 7.45825L12.3505 7.49159C12.2089 7.48325 12.1006 7.59992 12.1006 7.73325Z"
                  fill="#EDEEFE"
                />
              </svg>
            </div> */}
            <div className="flex justify-center items-center w-10 h-10 rounded-full backdrop-blur-xl bg-white/10">
              <a
                target="_blank"
                href="https://www.instagram.com/eventparkafrica"
              >
                <svg
                  width="21"
                  height="20"
                  viewBox="0 0 21 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    opacity="0.4"
                    d="M13.9915 1.66675H7.00817C3.97484 1.66675 2.1665 3.47508 2.1665 6.50841V13.4834C2.1665 16.5251 3.97484 18.3334 7.00817 18.3334H13.9832C17.0165 18.3334 18.8248 16.5251 18.8248 13.4917V6.50841C18.8332 3.47508 17.0248 1.66675 13.9915 1.66675Z"
                    fill="#EDEEFE"
                  />
                  <path
                    d="M10.4999 13.2333C12.2857 13.2333 13.7333 11.7857 13.7333 9.99994C13.7333 8.21421 12.2857 6.7666 10.4999 6.7666C8.71421 6.7666 7.2666 8.21421 7.2666 9.99994C7.2666 11.7857 8.71421 13.2333 10.4999 13.2333Z"
                    fill="#EDEEFE"
                  />
                  <path
                    d="M14.6668 6.24992C14.4418 6.24992 14.2335 6.16659 14.0752 6.00825C14.0002 5.92492 13.9418 5.83325 13.9002 5.73325C13.8585 5.63325 13.8335 5.52492 13.8335 5.41659C13.8335 5.30825 13.8585 5.19992 13.9002 5.09992C13.9418 4.99159 14.0002 4.90825 14.0752 4.82492C14.2668 4.63325 14.5585 4.54159 14.8252 4.59992C14.8835 4.60825 14.9335 4.62492 14.9835 4.64992C15.0335 4.66659 15.0835 4.69159 15.1335 4.72492C15.1752 4.74992 15.2168 4.79159 15.2585 4.82492C15.3335 4.90825 15.3918 4.99159 15.4335 5.09992C15.4752 5.19992 15.5002 5.30825 15.5002 5.41659C15.5002 5.52492 15.4752 5.63325 15.4335 5.73325C15.3918 5.83325 15.3335 5.92492 15.2585 6.00825C15.1752 6.08325 15.0835 6.14159 14.9835 6.18325C14.8835 6.22492 14.7752 6.24992 14.6668 6.24992Z"
                    fill="#EDEEFE"
                  />
                </svg>
              </a>
            </div>
            <div className="flex justify-center items-center w-10 h-10 rounded-full backdrop-blur-xl bg-white/10">
              <a target="_blank" href="https://vm.tiktok.com/ZMBWP9YLU/">
                <svg
                  width="15"
                  height="16"
                  viewBox="0 0 15 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M14.25 4.25008V5.75008C14.25 5.94899 14.171 6.13976 14.0303 6.28041C13.8897 6.42106 13.6989 6.50008 13.5 6.50008C12.4462 6.49821 11.4115 6.21884 10.5 5.69008V10.6251C10.5002 11.491 10.2698 12.3414 9.83244 13.0888C9.39506 13.8361 8.7665 14.4535 8.01141 14.8774C7.25632 15.3013 6.40194 15.5164 5.53615 15.5006C4.67035 15.4848 3.82438 15.2387 3.08524 14.7876C2.3461 14.3364 1.74046 13.6966 1.33061 12.9338C0.920762 12.171 0.72149 11.3128 0.753289 10.4474C0.785088 9.58205 1.04681 8.74078 1.51154 8.01011C1.97627 7.27944 2.62724 6.68575 3.3975 6.29008C3.51276 6.22868 3.64205 6.19844 3.77258 6.20235C3.90311 6.20627 4.03036 6.24419 4.14173 6.31238C4.2531 6.38057 4.34474 6.47665 4.40758 6.59113C4.47042 6.70561 4.50227 6.83451 4.5 6.96508V8.85508C4.49198 9.03122 4.41684 9.1976 4.29 9.32008C4.02065 9.59253 3.84162 9.94124 3.77718 10.3189C3.71274 10.6966 3.76601 11.0849 3.92977 11.4313C4.09353 11.7776 4.35986 12.0652 4.69263 12.2551C5.0254 12.4449 5.40851 12.5279 5.79 12.4926C6.26907 12.4316 6.7087 12.1957 7.02435 11.8302C7.34001 11.4647 7.50942 10.9954 7.5 10.5126V1.25008C7.5 1.05117 7.57902 0.860401 7.71967 0.719749C7.86032 0.579097 8.05109 0.500079 8.25 0.500079H9.8325C10.0112 0.497482 10.1849 0.558764 10.3224 0.672889C10.4599 0.787015 10.5521 0.946484 10.5825 1.12258C10.7245 1.79228 11.0914 2.39325 11.6221 2.82573C12.1528 3.2582 12.8154 3.49615 13.5 3.50008C13.6989 3.50008 13.8897 3.5791 14.0303 3.71975C14.171 3.8604 14.25 4.05117 14.25 4.25008Z"
                    fill="#EDEEFE"
                  />
                </svg>
              </a>
            </div>
            <div className="flex justify-center items-center w-10 h-10 rounded-full backdrop-blur-xl bg-white/10">
              <a
                target="_blank"
                href="https://www.linkedin.com/company/eventpark-ltd"
              >
                <svg
                  width="19"
                  height="20"
                  viewBox="0 0 19 20"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clipPath="url(#clip0_4880_36711)">
                    <path
                      d="M12.8335 6.6665C14.1596 6.6665 15.4313 7.19329 16.369 8.13097C17.3067 9.06865 17.8335 10.3404 17.8335 11.6665V17.4998H14.5002V11.6665C14.5002 11.2245 14.3246 10.8006 14.012 10.488C13.6994 10.1754 13.2755 9.99984 12.8335 9.99984C12.3915 9.99984 11.9675 10.1754 11.655 10.488C11.3424 10.8006 11.1668 11.2245 11.1668 11.6665V17.4998H7.8335V11.6665C7.8335 10.3404 8.36028 9.06865 9.29796 8.13097C10.2356 7.19329 11.5074 6.6665 12.8335 6.6665Z"
                      fill="#EDEEFE"
                      stroke="#EDEEFE"
                      strokeWidth="0.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M5.49984 7.5H2.1665V17.5H5.49984V7.5Z"
                      fill="#EDEEFE"
                      stroke="#EDEEFE"
                      strokeWidth="0.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M3.83317 5.00008C4.75364 5.00008 5.49984 4.25389 5.49984 3.33341C5.49984 2.41294 4.75364 1.66675 3.83317 1.66675C2.9127 1.66675 2.1665 2.41294 2.1665 3.33341C2.1665 4.25389 2.9127 5.00008 3.83317 5.00008Z"
                      fill="#EDEEFE"
                      stroke="#EDEEFE"
                      strokeWidth="0.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_4880_36711">
                      <rect
                        width="18"
                        height="20"
                        fill="white"
                        transform="translate(0.5)"
                      />
                    </clipPath>
                  </defs>
                </svg>
              </a>
            </div>
            <div className="flex justify-center items-center w-10 h-10 rounded-full backdrop-blur-xl bg-white/10">
              <a target="_blank" href="https://twitter.com/EventParkAfrica">
                <svg
                  width="15"
                  height="16"
                  viewBox="0 0 15 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8.89682 6.87774L14.3574 0.666748H13.0636L8.32003 6.05854L4.53411 0.666748H0.166504L5.89283 8.82081L0.166504 15.3334H1.46032L6.46656 9.63817L10.4656 15.3334H14.8332L8.89682 6.87774ZM7.64097 6.83552L8.21961 7.64799L13.063 14.4255L7.64097 6.83552Z"
                    fill="#EDEEFE"
                  />
                </svg>
              </a>
            </div>
          </div>
          <div className="mx-auto max-w-7xl relative mt-[170px]  z-[100]">
            <nav className="flex justify-center">
              <ul className="flex px-8 py-3 space-x-12 rounded-full shadow-xl">
                <li>
                  <a
                    href="/"
                    className="text-[10px] sm:text-[12px] md:text-sm font-medium text-white hover:text-[#4D55F2]"
                  >
                    HOME
                  </a>
                </li>
                {/* <li>
              <a
                href="#"
                className="text-[10px] sm:text-[12px] md:text-sm font-medium text-white hover:text-[#4D55F2]"
              >
                COMPANY
              </a>
            </li> */}
                <li>
                  <a
                    href="/vendor"
                    className="text-[10px] sm:text-[12px] md:text-sm font-medium text-white hover:text-[#4D55F2]"
                  >
                    FOR VENDORS
                  </a>
                </li>
                <li>
                  <a
                    href="/marketplace"
                    className="text-[10px] sm:text-[12px] md:text-sm font-medium text-white hover:text-[#4D55F2]"
                  >
                    MARKETPLACE
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>

      {/* Contact Form and Info Section */}
      <div className="bg-[#F5F9FF] py-16">
        <div className="container px-4 mx-auto max-w-7xl">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            {/* Contact Form */}
            <div className="p-8 bg-white rounded-2xl shadow-xl">
              <h2 className="text-[#4D55F2] text-xl md:text-[40px] font-normal mb-6">
                Send A Message
              </h2>
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label
                    htmlFor="fullName"
                    className="block mb-2 text-sm font-medium text-gray-700"
                  >
                    Fullname
                  </label>
                  <input
                    type="text"
                    id="fullName"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border placeholder:text-[#717680] border-[#D5D7DA] rounded-[64px] focus:outline-none focus:ring-1 focus:ring-[#4D55F2] focus:border-[#4D55F2]"
                    placeholder="John Doe"
                    required
                  />
                </div>
                <div className="mb-4">
                  <label
                    htmlFor="email"
                    className="block mb-2 text-sm font-medium text-gray-700"
                  >
                    Email
                  </label>
                  <div className="relative">
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-[#D5D7DA] rounded-[64px] focus:outline-none placeholder:text-[#717680] focus:ring-1 focus:ring-[#4D55F2] focus:border-[#4D55F2]"
                      placeholder="<EMAIL>"
                      required
                    />
                    <div className="flex absolute inset-y-0 right-0 items-center pr-3">
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
                          stroke="#FF3B30"
                          stroke-width="1.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M12 8V13"
                          stroke="#FF3B30"
                          stroke-width="1.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M11.9946 16H12.0036"
                          stroke="#FF3B30"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                <div className="mb-6">
                  <label
                    htmlFor="message"
                    className="block mb-2 text-sm font-medium text-gray-700"
                  >
                    Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows="4"
                    className="w-full px-4 py-3 border border-[#D5D7DA] rounded-xl focus:outline-none focus:ring-1 focus:ring-[#4D55F2] focus:border-[#4D55F2]"
                    placeholder="Write a message"
                    required
                  ></textarea>
                  <p className="mt-2 text-xs text-gray-500">
                    This is a hint text to help user.
                  </p>
                </div>
                <button
                  type="submit"
                  className="w-full bg-[#4D55F2] text-white py-3 px-4 rounded-full hover:bg-[#3A41B3] transition duration-300 font-medium"
                >
                  Send Message
                </button>
              </form>
            </div>

            {/* Contact Info Cards */}
            <div className="grid grid-cols-2 gap-6">
              <div className="bg-[#CDE0FD] flex flex-col  justify-end  p-6 rounded-xl">
                <h3 className="mb-1 text-sm font-medium tracking-wider text-[#8E8E93] uppercase">
                  FAQs
                </h3>
                <h2 className="mb-2 text-[20px] md:text-[28px] font-medium">
                  Got Questions?
                </h2>
                <p className="mb-4 text-[16px] text-[#8e8e93]">
                  We've got answers
                </p>
                <a href="/faq">
                  <button className="bg-[#9CC1FC] h-[36px] w-[106px] text-[black] px-4 py-2 rounded-full text-sm font-semibold min-w-max  hover:bg-[#9CC1FC]/80 transition duration-200">
                    Check FAQs
                  </button>
                </a>
              </div>

              <div className="bg-[#CDE0FD] p-6 rounded-xl flex flex-col justify-end">
                <h3 className="mb-1 text-sm font-medium tracking-wider text-[#8E8E93] uppercase">
                  HELP CENTER
                </h3>
                <h2 className="mb-2 text-[20px] md:text-[28px] font-medium">
                  Need Help?
                </h2>
                <p className="mb-4 text-[16px] text-[#8e8e93]">
                  Check out our Help Center
                </p>
                <button className="bg-[#9CC1FC] h-[36px] text-[black] px-4 py-2 rounded-full text-sm hidden font-semibold min-w-max w-[130px] transition duration-200">
                  Visit Help Center
                </button>
              </div>

              <div className="bg-[#CDE0FD] flex flex-col justify-center col-span-2 p-6 rounded-xl">
                <h3 className="mb-1 text-sm font-medium tracking-wider text-[#8E8E93] uppercase">
                  OUR BLOG
                </h3>
                <h2 className="mb-2 text-[28px] leading-[125%] text-[#000000] italic  md:text-[28px] font-medium">
                  Get event planning tips and updates straight to your inbox
                </h2>
                <p className="mb-4 text-[16px] text-[#8e8e93]">
                  We've got answers
                </p>
                <button className="bg-[#9CC1FC] h-[36px] text-[black] px-4 py-2 rounded-full hidden text-sm font-semibold w-[130px] min-w-max hover:bg-[#F5F6FE] transition duration-200">
                  Read from our Blog
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div
        className="w-full mt-[40px] md:mt-[30px] py-[35px] sm:py-[70px] md:py-[140px]"
        style={{
          background:
            "linear-gradient(to bottom, #FEFAF8 0%, #F5F6FE 50%, #FFFFFF 100%)",
        }}
      >
        <div id="faqs" className="container px-4 mx-auto max-w-7xl">
          {/* FAQ Header */}
          <h4 className="text-[15px] md:hidden text-[#B3B3B3] trackig-wide mb-3 leading-normal text-center font-medium">
            FAQS
          </h4>
          <div className="mb-16 text-center mx-auto w-[90%] md:w-[662px]">
            <h2 className="md:font-medium text-[28px] sm:text-[36px] md:text-[48px] mb-4 tracking-tight leading-[125%] text-black">
              We answered your questions so you don't have to ask again
            </h2>
          </div>

          {/* FAQ Items */}
          <div className="mx-auto max-w-3xl mt-[60px]">
            {faqItems.map((item, index) => (
              <div
                key={index}
                className={`sm:pb-6 pb-5 py-[8px] px-5 lg:px-0 sm:pt-[10px] mb-6 ${
                  openIndex === index ? "" : "border-b border-[#E9EBF1]"
                }`}
              >
                <button
                  className="flex justify-between items-center w-full text-left focus:outline-none"
                  onClick={() => toggleFAQ(index)}
                >
                  <h3
                    className={`${
                      openIndex === index
                        ? "text-[24px] text-[#0109A5] font-bold text-left tracking-tight"
                        : "text-[20px] text-left tracking-tight text-[#808084] font-normal"
                    }`}
                  >
                    {item.question}
                  </h3>
                  {openIndex === index ? (
                    <svg
                      className="cursor-pointer"
                      width="22"
                      height="2"
                      viewBox="0 0 22 2"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1.66663 1H20.3333"
                        stroke="black"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  ) : (
                    <svg
                      className="cursor-pointer"
                      width="22"
                      height="22"
                      viewBox="0 0 22 22"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M11 1.6665V20.3332M1.66663 10.9998H20.3333"
                        stroke="black"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  )}
                </button>
                <div
                  className={`mt-[10px] overflow-hidden transition-all duration-300 ${
                    openIndex === index
                      ? "max-h-96 font-normal py-[32px] px-[20px] text-[20px] tracking-[normal] bg-white rounded-xl text-[#31312F]"
                      : "max-h-0"
                  }`}
                >
                  <p className="">{item.answer}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-10 text-center">
            <a href="/faq">
              <button className="inline-flex gap-2 items-center px-8 py-3 text-sm font-medium text-white bg-black rounded-full shadow-lg">
                Check out FAQs
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    opacity="0.4"
                    d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                    fill="white"
                  />
                  <path
                    d="M16.03 11.4699L13.03 8.46994C12.74 8.17994 12.26 8.17994 11.97 8.46994C11.68 8.75994 11.68 9.23994 11.97 9.52994L13.69 11.2499H8.5C8.09 11.2499 7.75 11.5899 7.75 11.9999C7.75 12.4099 8.09 12.7499 8.5 12.7499H13.69L11.97 14.4699C11.68 14.7599 11.68 15.2399 11.97 15.5299C12.12 15.6799 12.31 15.7499 12.5 15.7499C12.69 15.7499 12.88 15.6799 13.03 15.5299L16.03 12.5299C16.32 12.2399 16.32 11.7599 16.03 11.4699Z"
                    fill="white"
                  />
                </svg>
              </button>
            </a>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
