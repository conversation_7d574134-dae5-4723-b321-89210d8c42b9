import React, { useState } from "react";

export function ContactSection() {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    message: "",
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission logic here
    console.log("Form submitted:", formData);
    // Reset form
    setFormData({
      fullName: "",
      email: "",
      message: "",
    });
  };

  return (
    <div className="bg-[#F5F9FF] py-12 rounded-2xl max-w-7xl mx-auto my-16">
      <div className="container px-4 mx-auto">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-[1fr,1fr]">
          {/* Contact Form */}
          <div className="p-8 bg-white rounded-2xl shadow-md">
            <h2 className="text-[#6366F1] text-xl font-semibold mb-6">
              Send A Message
            </h2>
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label
                  htmlFor="fullName"
                  className="block mb-2 text-sm font-medium text-gray-600"
                >
                  Fullname
                </label>
                <input
                  type="text"
                  id="fullName"
                  name="fullName"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#6366F1] focus:border-[#6366F1]"
                  placeholder="John Doe"
                  required
                />
              </div>
              <div className="mb-4">
                <label
                  htmlFor="email"
                  className="block mb-2 text-sm font-medium text-gray-600"
                >
                  Email
                </label>
                <div className="relative">
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#6366F1] focus:border-[#6366F1]"
                    placeholder="<EMAIL>"
                    required
                  />
                  <div className="flex absolute inset-y-0 right-0 items-center pr-3">
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 20 20"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle cx="10" cy="10" r="10" fill="#FDA29B" />
                      <path
                        d="M6 10L8.5 12.5L14 7"
                        stroke="white"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <div className="mb-6">
                <label
                  htmlFor="message"
                  className="block mb-2 text-sm font-medium text-gray-600"
                >
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows="4"
                  className="w-full px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-1 focus:ring-[#6366F1] focus:border-[#6366F1]"
                  placeholder="Write a message"
                  required
                ></textarea>
                <p className="mt-2 text-xs text-gray-500">
                  This is a hint text to help user.
                </p>
              </div>
              <button
                type="submit"
                className="w-full bg-[#6366F1] text-white py-3 px-4 rounded-full hover:bg-[#4F46E5] transition duration-300 font-medium"
              >
                Send Message
              </button>
            </form>
          </div>

          {/* Contact Info Cards */}
          <div className="grid grid-cols-1 gap-4">
            {/* Top row: two cards side by side */}
            <div className="grid grid-cols-2 gap-4">
              {/* Got Questions? Card */}
              <div className="bg-[#E6EEFF] p-6 rounded-xl flex flex-col h-full">
                <div className="flex-grow">
                  <h3 className="mb-1 text-xs tracking-wider text-gray-500 uppercase">
                    FAQs
                  </h3>
                  <h2 className="mb-2 text-lg font-semibold">Got Questions?</h2>
                </div>
                <button className="bg-white text-[#6366F1] px-4 py-2 rounded-full text-sm font-medium hover:bg-[#F5F6FE] transition duration-200 mt-auto">
                  Check FAQs
                </button>
              </div>

              {/* Need Help? Card */}
              <div className="bg-[#E6EEFF] p-6 rounded-xl flex flex-col h-full">
                <div className="flex-grow">
                  <h3 className="mb-1 text-xs tracking-wider text-gray-500 uppercase">
                    HELP CENTER
                  </h3>
                  <h2 className="mb-2 text-lg font-semibold">Need Help?</h2>
                </div>
                {/* <button className="bg-white text-[#6366F1] px-4 py-2 rounded-full text-sm font-medium hover:bg-[#F5F6FE] transition duration-200 mt-auto">
                  Visit Help Center
                </button> */}
              </div>
            </div>

            {/* Blog Card - Full width */}
            <div className="bg-[#E6EEFF] p-6 rounded-xl flex flex-col h-full">
              <div className="flex-grow">
                <h3 className="mb-1 text-xs tracking-wider text-gray-500 uppercase">
                  OUR BLOG
                </h3>
                <h2 className="mb-2 text-lg font-semibold">
                  Get event planning tips and updates straight to your inbox
                </h2>
              </div>
              {/* <button className="bg-white text-[#6366F1] px-4 py-2 rounded-full text-sm font-medium hover:bg-[#F5F6FE] transition duration-200 mt-auto w-fit">
                Read from our Blog
              </button> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
