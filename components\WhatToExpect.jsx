import React from "react";

export function WhatToExpect() {
  return (
    <div className="relative w-full">
      {/* Curved top section - positioned to create the rounded top effect */}
      <div className="absolute top-[-30px] left-0 w-full overflow-hidden z-10">
        <img
          src="/curved-top.svg"
          alt=""
          className="w-full mb-[30px] xl:mb-0 sm:mt-[16px] mt-[24px] md:mt-[8px] lg:mt-[4px] xl:mt-0"
        />
      </div>

      {/* Main content with gradient background */}
      <div
        className=" sm:pt-[2rem] pt-2 pb-2"
        style={{
          background: "linear-gradient(to bottom, #000026 0%, #00008C 100%)",
          width: "100%",

          position: "relative",
          marginTop: "-2px",
        }}
      >
        <div className="container h-[440px] sm:h-[824px] flex flex-col justify-center mx-auto px-4 max-w-7xl text-center text-white">
          {/* Header */}
          <div className="uppercase tracking-wider text-[13px] sm:text-[20px] mb-12 flex items-center justify-start gap-2 text-[#B0CDFD] relative z-[10] font-medium ">
            <span className="tracking-[3px]">EVENTPARK</span>
            <span className="text-xs">•</span>
            <span className="tracking-[3px]">WHAT TO EXPECT!</span>
          </div>

          {/* Main content */}
          <div className="max-w-3xl">
            <h2 className="sm:text-[36px] md:text-[40px] text-[20px] relative z-[10] leading-[165%] font-normal text-left  mb-8 ">
              EventPark is your ultimate event planning companion. Find top
              vendors, manage budgets, and book seamlessly. We're just getting
              started
            </h2>
          </div>
        </div>

        {/* Background decorative elements */}
        <div className="overflow-hidden absolute inset-0 pointer-events-none">
          <img
            src="/what-expect.svg"
            alt=""
            className="object-fill absolute w-full h-full"
          />
        </div>
      </div>
    </div>
  );
}
