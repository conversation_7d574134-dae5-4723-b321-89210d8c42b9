import React, { useState } from "react";

export function FAQ() {
  // State to track which FAQ item is open
  const [openIndex, setOpenIndex] = useState(0);

  // Toggle function for FAQ items
  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? -1 : index);
  };

  // FAQ data
  const faqItems = [
    {
      question: "What is EventPark?",
      answer:
        "EventPark is an event management platform that streamlines the process of finding, booking, and managing vendors for events.",
    },
    {
      question: "How do I create an event?",
      answer:
        "Creating an event is simple! Sign up for an account, click on 'Create Event' from your dashboard, and follow the guided steps to set up your event details, guest list, and more.",
    },
    {
      question: "Is EventPark free to use?",
      answer:
        "EventPark offers both free and premium plans. The basic features are available at no cost, while our premium plans provide additional capabilities for more complex events and professional event planners.",
    },
    {
      question: "Can I manage multiple events at once?",
      answer:
        "Absolutely! EventPark allows you to create and manage multiple events simultaneously from a single dashboard, making it perfect for event planners or individuals organizing several gatherings.",
    },
    {
      question: "How secure is my event information?",
      answer:
        "We take security seriously. All your event data and guest information is encrypted and stored securely. We never share your information with third parties without your explicit permission.",
    },
  ];

  return (
    <div
      className="w-full mt-[40px] md:mt-[30px] py-[35px] sm:py-[70px]  md:py-[140px]"
      style={{
        background:
          "linear-gradient(to bottom, #FEFAF8 0%, #F5F6FE 50%, #FFFFFF 100%)",
      }}
    >
      <div id="faqs" className="container px-4 mx-auto max-w-7xl">
        {/* FAQ Header */}
        <h4 className="text-[15px] md:hidden text-[#B3B3B3] trackig-wide mb-3 leading-normal text-center font-medium">
          FAQS
        </h4>
        <div className="mb-16 text-center mx-auto  w-[90%] md:w-[662px] ">
          <h2 className="md:font-medium text-[28px] sm:text-[36px] md:text-[48px] mb-4 tracking-tight leading-[125%] text-black">
            We answered your questions so you don’t have to ask again
          </h2>
        </div>

        {/* FAQ Items */}
        <div className="mx-auto max-w-3xl mt-[60px]">
          {faqItems.map((item, index) => (
            <div
              key={index}
              className={`sm:pb-6 pb-5 py-[8px] px-5 lg:px-0 sm:pt-[10px] mb-6  ${
                openIndex === index ? "" : "border-b border-[#E9EBF1]"
              }`}
            >
              <button
                className="flex justify-between items-center w-full text-left focus:outline-none"
                onClick={() => toggleFAQ(index)}
              >
                <h3
                  className={`${
                    openIndex === index
                      ? "text-[24px] text-[#0109A5]  font-bold text-left  tracking-tight"
                      : "text-[20px] text-left tracking-tight text-[#808084] font-normal"
                  } `}
                >
                  {item.question}
                </h3>
                {openIndex === index ? (
                  <svg
                    className="cursor-pointer"
                    width="22"
                    height="2"
                    viewBox="0 0 22 2"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M1.66663 1H20.3333"
                      stroke="black"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                ) : (
                  <svg
                    className="cursor-pointer"
                    width="22"
                    height="22"
                    viewBox="0 0 22 22"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M11 1.6665V20.3332M1.66663 10.9998H20.3333"
                      stroke="black"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                )}
              </button>
              <div
                className={`mt-[10px]  overflow-hidden transition-all duration-300 ${
                  openIndex === index
                    ? "max-h-96 font-normal py-[32px] px-[20px] text-[20px] tracking-[normal] bg-white rounded-xl text-[#31312F]"
                    : "max-h-0"
                }`}
              >
                <p className={``}>{item.answer}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
