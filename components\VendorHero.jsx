import React, { useRef, useState } from "react";
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";
// Import Swiper styles
import "swiper/css";
// Import custom styles
import "./HeroCarousel.css";
// Import required modules
import { Autoplay, EffectCoverflow } from "swiper/modules";
// Import WaitlistModal
import WaitlistModal from "./WaitlistModal";
// Import custom navigation hook
import { useClientNavigation } from "../renderer/useClientNavigation";

// Import Swiper styles
import "swiper/css";
import "swiper/css/effect-coverflow";

export function VendorHero() {
  const [activeTab, setActiveTab] = useState("forVendors");
  const [showWaitlistModal, setShowWaitlistModal] = useState(false);
  const [swiperInitialized, setSwiperInitialized] = useState(false);
  const { navigate } = useClientNavigation();

  const vendorImages = [
    "/vendor-1.png",
    "/vendor-2.png",
    "/vendor-3.png",
    "/vendor-4.jpg",
    "/vendor-5.png",
    "/vendor-6.jpg",
    "/vendor-7.jpg",
  ];

  const swiperRef = useRef(null);

  const handleOpenWaitlist = (e) => {
    e.preventDefault();
    setShowWaitlistModal(true);
  };

  const handleCloseWaitlist = () => {
    setShowWaitlistModal(false);
  };

  return (
    <>
      {" "}
      {showWaitlistModal && <WaitlistModal onClose={handleCloseWaitlist} />}
      <div className="bg-gradient-to-b from-[#FEF7F4] to-[#EBF3FE] w-full">
        <div className="container py-12 pt-[60px] mx-auto max-w-7xl">
          <div className="flex flex-col items-center text-center">
            {/* Logo and Navigation */}
            <div className="flex justify-center px-4 mb-8 w-full">
              <div className="flex flex-col items-center">
                <div className="text-[#000073] font-bold text-[24px] tracking-[-0.01px] mb-6">
                  <div>
                    <span className="inline-flex relative gap-1 item">
                      <img src="/logo.svg" alt="" />
                      EventPark
                      <span className="min-w-[4px] w-[4px] h-[4px] min-h-[4px] rounded-full bg-[#FF5519] absolute bottom-[10px] right-[-8px]"></span>
                    </span>
                  </div>
                </div>

                {/* Navigation Tabs */}
                <div className="flex gap-8 mb-[28px] bg-[#FDEFE9] p-2 rounded-full">
                  <div className="flex relative flex-col items-center">
                    <div
                      onClick={() => {
                        setActiveTab("forYou");
                        navigate("/");
                      }}
                      className={`flex h-[36px] justify-center items-center ${
                        activeTab === "forYou" ? "bg-white" : ""
                      } px-[9px] transition-all duration-500 ease-out rounded-full w-[77px] py-4 cursor-pointer`}
                    >
                      <span
                        className={`text-[14px] tracking-[-2%] transition-all duration-500 ease-out ${
                          activeTab === "forYou"
                            ? "text-[#FF6630] font-bold"
                            : "text-[#666666]"
                        }`}
                      >
                        For You
                      </span>
                    </div>
                    {activeTab === "forYou" && (
                      <div className="min-h-[4px] absolute transition-all duration-500 ease-out bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
                    )}
                  </div>

                  <div className="flex relative flex-col items-center">
                    <div
                      className={`flex h-[36px] justify-center items-center ${
                        activeTab === "forVendors" ? "bg-white" : ""
                      } px-[9px] rounded-full w-[108px] py-4 transition-all duration-500 ease-out cursor-pointer`}
                      onClick={() => setActiveTab("forVendors")}
                    >
                      <span
                        className={`text-[14px] tracking-[-2%] transition-all duration-500 ease-out ${
                          activeTab === "forVendors"
                            ? "text-[#FF6630] font-bold"
                            : "text-[#666666]"
                        }`}
                      >
                        For Vendors
                      </span>
                    </div>
                    {activeTab === "forVendors" && (
                      <div className="min-h-[4px] absolute bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
                    )}
                  </div>

                  <div className="flex relative flex-col items-center">
                    <div
                      className={`flex h-[36px] justify-center items-center ${
                        activeTab === "marketplace" ? "bg-white" : ""
                      } px-[9px] rounded-full w-[108px] py-4 transition-all duration-500 ease-out cursor-pointer`}
                      onClick={() => {
                        setActiveTab("marketplace");
                        navigate("/marketplace");
                      }}
                    >
                      <span
                        className={`text-[14px] tracking-[-2%] transition-all duration-500 ease-out ${
                          activeTab === "marketplace"
                            ? "text-[#FF6630] font-bold"
                            : "text-[#666666]"
                        }`}
                      >
                        Marketplace
                      </span>
                    </div>
                    {activeTab === "marketplace" && (
                      <div className="min-h-[4px] absolute bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Hero Content */}
            <div className="container mx-auto">
              <h1 className="sm:text-6xl px-4 text-[52px] leading-[95%] lg:leading-[95px] flex flex-col md:text-7xl lg:text-[80px] tracking-[-2.8px] font-bold mb-4">
                <span className="text-[#FF8A65]">Reach More</span>
                <span className="text-[#800000] text-[60px] leading-[70px] lg:text-[110px] lg:leading-[95%]">
                  With Less
                </span>
              </h1>

              <p className="text-[#666666] px-4 mt-7 text-[18px] leading-[150%] tracking-[0.1px] mb-7 w-[272px] sm:w-[500px] max-w-[272px] sm:max-w-2xl mx-auto">
                A sneak peek at the future of event planning—powerful tools you
                can try today with even more coming soon!
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col gap-6 justify-center items-center px-4 mb-16">
                <a
                  href="#"
                  onClick={handleOpenWaitlist}
                  className="bg-[#4D55F2] text-white text-[18px] font-bold hover:bg-[#4D55F2]/90 h-[56px] transition-colors duration-300 ease-out py-3 px-8 rounded-full flex items-center"
                >
                  Explore Early Features
                  <span className="ml-[0.5px]">🚀</span>
                </a>
                {/* <a
                  href="#"
                  onClick={handleOpenWaitlist}
                  className="text-[#FF5519] font-extrabold hover:text-[hsl(16,100%,45%)] leading-[125%] italic flex items-center"
                >
                  Sign In Here
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6 9.32999V14.67C6 17.99 8.35 19.34 11.22 17.69L12.5 16.95C12.81 16.77 13 16.44 13 16.08V7.91999C13 7.55999 12.81 7.22999 12.5 7.04999L11.22 6.30999C8.35 4.65999 6 6.00999 6 9.32999Z"
                      fill="#FF5519"
                    />
                    <path
                      opacity="0.4"
                      d="M14 8.79V15.22C14 15.61 14.42 15.85 14.75 15.65L15.85 15.01C18.72 13.36 18.72 10.64 15.85 8.99L14.75 8.35C14.42 8.16 14 8.4 14 8.79Z"
                      fill="#FF5519"
                    />
                  </svg>
                </a> */}
              </div>

              {/* Vendor Images - Fixed Carousel with exactly 5 slides visible */}
              <div className="w-full mb-[80px] mt-12 relative">
                <div className="overflow-hidden hero-swiper-container">
                  <Swiper
                    slidesPerView={4.3}
                    spaceBetween={40}
                    centeredSlides={true}
                    loop={true}
                    speed={800}
                    // loopAdditionalSlides={10}
                    // loopFillGroupWithBlank={true}
                    watchSlidesProgress={true}
                    grabCursor={true}
                    autoplay={{
                      delay: 3000,
                      disableOnInteraction: false,
                      pauseOnMouseEnter: true,
                    }}
                    breakpoints={{
                      320: {
                        slidesPerView: 3,
                        spaceBetween: 10,
                      },
                      640: {
                        slidesPerView: 5,
                        spaceBetween: 10,
                      },
                      1024: {
                        slidesPerView: 5,
                        spaceBetween: 40,
                      },
                    }}
                    modules={[Autoplay]}
                    className="mySwiper"
                    onSwiper={(swiper) => {
                      swiperRef.current = swiper;
                      setSwiperInitialized(true);
                    }}
                  >
                    {/* Add extra slides at the beginning and end for smoother looping */}
                    {[...vendorImages, ...vendorImages, ...vendorImages].map(
                      (img, index) => (
                        <SwiperSlide key={index}>
                          <div className="flex justify-center items-center">
                            {swiperInitialized ? (
                              <img
                                src={img}
                                loading="lazy"
                                alt={`Vendor service ${
                                  (index % vendorImages.length) + 1
                                }`}
                                className="object-cover rounded-3xl shadow-md"
                              />
                            ) : (
                              <div className="w-[330px] h-[330px] bg-gray-200 rounded-3xl animate-pulse"></div>
                            )}
                          </div>
                        </SwiperSlide>
                      )
                    )}
                  </Swiper>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
