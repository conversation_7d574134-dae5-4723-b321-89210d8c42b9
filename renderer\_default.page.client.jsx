// renderer/_default.page.client.jsx
import React from "react";
import ReactDOM from "react-dom/client";
import { PageLayout } from "./PageLayout";
import { PageContextProvider } from "./usePageContext";
import "./index.css";

export { render };

async function render(pageContext) {
  const { Page, pageProps } = pageContext;

  // This render() hook only supports pages that export a React component as their default export
  if (!Page)
    throw new Error("My render() hook expects pageContext.Page to be defined");

  ReactDOM.hydrateRoot(
    document.getElementById("page-view"),
    <PageContextProvider pageContext={pageContext}>
      <PageLayout>
        <Page {...pageProps} />
      </PageLayout>
    </PageContextProvider>
  );
}
