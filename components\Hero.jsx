import React, { useState } from "react";
// Import Swiper React components
import { Swiper, SwiperSlide } from "swiper/react";
// Import Swiper styles
import "swiper/css";
// Import custom styles
import "./HeroCarousel.css";
// Import required modules
import { Autoplay } from "swiper/modules";
// Import UserWaitlistModal
import UserWaitlistModal from "./UserWaitlistModal";
// Import custom navigation hook
import { useClientNavigation } from "../renderer/useClientNavigation";

export function Hero() {
  const [activeTab, setActiveTab] = useState("forYou");
  const [showWaitlistModal, setShowWaitlistModal] = useState(false);
  const { navigate } = useClientNavigation();

  // const [swiperInitialized, setSwiperInitialized] = useState(false);
  // const swiperRef = useRef(null);
  return (
    <>
      {" "}
      {showWaitlistModal && (
        <UserWaitlistModal onClose={() => setShowWaitlistModal(false)} />
      )}
      <div className="bg-gradient-to-b from-[#FEF7F4] to-[#EBF3FE] w-full">
        <div className="container  py-12 pt-[60px] mx-auto max-w-7xl">
          <div className="flex flex-col items-center text-center">
            {/* Logo and Navigation */}
            <div className="flex justify-center px-4 mb-8 w-full">
              <div className="flex flex-col items-center">
                <div className="text-[#000073] font-bold text-[24px] tracking-[-0.01px] mb-6">
                  <div>
                    <span className="inline-flex relative gap-1 item">
                      <img src="/logo.svg" alt="" />
                      EventPark
                      <span className=" min-w-[4px] w-[4px] h-[4px] min-h-[4px] rounded-full bg-[#FF5519] absolute bottom-[10px] right-[-8px]"></span>
                    </span>
                  </div>
                </div>

                {/* Navigation Tabs */}
                <div className="flex gap-8 mb-[28px] bg-[#FDEFE9] p-2 rounded-full">
                  <div className="flex relative flex-col items-center">
                    <div
                      className={`flex h-[36px] justify-center items-center ${
                        activeTab === "forYou" ? "bg-white" : ""
                      } px-[9px]  transition-all duration-500 ease-out rounded-full w-[77px] py-4 cursor-pointer`}
                      onClick={() => setActiveTab("forYou")}
                    >
                      <span
                        className={`text-[14px] tracking-[-2%] transition-all duration-500 ease-out  ${
                          activeTab === "forYou"
                            ? "text-[#4d55f2] font-bold"
                            : "text-[#634C42]"
                        }`}
                      >
                        For You
                      </span>
                    </div>
                    {activeTab === "forYou" && (
                      <div className="min-h-[4px] absolute transition-all duration-500 ease-out bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
                    )}
                  </div>

                  <div className="flex relative flex-col items-center">
                    <div
                      onClick={() => {
                        setActiveTab("forVendors");
                        navigate("/vendor");
                      }}
                      className={`flex h-[36px] justify-center items-center ${
                        activeTab === "forVendors" ? "bg-white" : ""
                      } px-[9px] rounded-full w-[108px] py-4 transition-all duration-500 ease-out cursor-pointer`}
                    >
                      <span
                        className={`text-[14px] tracking-[-2%] transition-all duration-500 ease-out ${
                          activeTab === "forVendors"
                            ? "text-[#4d55f2] font-bold"
                            : "text-[#634C42]"
                        }`}
                      >
                        For Vendors
                      </span>
                    </div>
                    {activeTab === "forVendors" && (
                      <div className="min-h-[4px] absolute bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
                    )}
                  </div>

                  <div className="flex relative flex-col items-center">
                    <div
                      onClick={() => {
                        setActiveTab("marketplace");
                        navigate("/marketplace");
                      }}
                      className={`flex h-[36px] justify-center items-center ${
                        activeTab === "marketplace" ? "bg-white" : ""
                      } px-[9px] rounded-full w-[108px] py-4 transition-all duration-500 ease-out cursor-pointer`}
                    >
                      <span
                        className={`text-[14px] tracking-[-2%] transition-all duration-500 ease-out ${
                          activeTab === "marketplace"
                            ? "text-[#4d55f2] font-bold"
                            : "text-[#634C42]"
                        }`}
                      >
                        Marketplace
                      </span>
                    </div>
                    {activeTab === "marketplace" && (
                      <div className="min-h-[4px] absolute bottom-[-16px] left-[50%] min-w-[4px] bg-[#FF5519] rounded-full mt-1"></div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Hero Content */}
            <div className="container mx-auto">
              <h1 className="sm:text-6xl px-4 text-[52px] leading-[65px] lg:leading-[95px] flex flex-col md:text-7xl lg:text-[90px] tracking-[-2.8px]  font-bold mb-4">
                <span className="bg-gradient-to-r from-[#FFBBA3] to-[#A6AAF9] bg-clip-text text-transparent">
                  Plan Events
                </span>

                <span className="text-[#000073] text-[60px] leading-[70px] lg:text-[100px] lg:leading-[95px]">
                  Effortlessly
                </span>
              </h1>

              <p className="text-[#666666] px-4 mt-7 text-[18px] leading-[150%] tracking-[0.1px] mb-7 w-[272px] sm:w-[500px] max-w-[272px] sm:max-w-2xl mx-auto">
                A sneak peek at the future of event planning—powerful tools you
                can try today, with even more coming soon!
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col gap-6 justify-center items-center px-4 mb-16">
                <button
                  onClick={() => setShowWaitlistModal(true)}
                  className="bg-[#4D55F2] text-white  text-[18px] font-bold hover:bg-[#4D55F2]/90  h-[56px] transition-colors duration-300 ease-out   py-3 px-8 rounded-full flex items-center"
                >
                  Explore Early Features
                  <span className="ml-[0.5px]">🚀</span>
                </button>
                {/* <button
                  onClick={() => setShowWaitlistModal(true)}
                  className="text-[#FF5519] font-extrabold hover:text-[hsl(16,100%,45%)] leading-[125%] italic flex items-center"
                >
                  Sign In Here
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6 9.32999V14.67C6 17.99 8.35 19.34 11.22 17.69L12.5 16.95C12.81 16.77 13 16.44 13 16.08V7.91999C13 7.55999 12.81 7.22999 12.5 7.04999L11.22 6.30999C8.35 4.65999 6 6.00999 6 9.32999Z"
                      fill="#FF5519"
                    />
                    <path
                      opacity="0.4"
                      d="M14 8.79V15.22C14 15.61 14.42 15.85 14.75 15.65L15.85 15.01C18.72 13.36 18.72 10.64 15.85 8.99L14.75 8.35C14.42 8.16 14 8.4 14 8.79Z"
                      fill="#FF5519"
                    />
                  </svg>
                </button> */}
              </div>

              {/* Event Images */}
              <div className="flex items-center gap-[15px] mb-[80px]">
                <div className="lg:min-w-[112px] lg:min-h-[228px] h-[68px] hidden md:block w-[68px] bg-[#d7e6fe] rounded-[45px]"></div>

                {/* Desktop View - Grid Layout */}
                <div className="hidden overflow-y-auto grid-cols-3 gap-6 mt-12 sm:grid scrollbar-hide">
                  <div className="sm:translate-x-[unset] w-auto rounded-3xl">
                    <img
                      src="/hero1.svg"
                      alt="Event planning"
                      className="w-full h-[449px] object-cover"
                    />
                  </div>
                  <div className="w-auto rounded-3xl">
                    <img
                      src="/hero2.svg"
                      alt="Party celebration"
                      className="w-full h-[449px] object-cover"
                    />
                  </div>
                  <div className="sm:translate-x-[unset] rounded-3xl w-auto">
                    <img
                      src="/hero3.svg"
                      alt="Wedding event"
                      className="w-full h-[449px] object-cover"
                    />
                  </div>
                </div>

                {/* Mobile View - Swiper Carousel */}
                <div className="mt-12 w-full sm:hidden hero-swiper-container">
                  <Swiper
                    spaceBetween={15}
                    slidesPerView={1.7}
                    centeredSlides={true}
                    loop={false}
                    speed={200}
                    centeredSlidesBounds={true}
                    // loopAddBlankSlides={true}
                    // loopAdditionalSlides={true}
                    autoplay={{
                      delay: 3000,
                      reverseDirection: true,
                      disableOnInteraction: false,
                    }}
                    modules={[Autoplay]}
                    className="w-full mySwiper"
                  >
                    <SwiperSlide>
                      <div className="w-[220px] mx-auto rounded-3xl">
                        <img
                          src="/hero1.svg"
                          alt="Event planning"
                          className="w-full h-[250px] object-cover rounded-3xl"
                        />
                      </div>
                    </SwiperSlide>
                    <SwiperSlide>
                      <div className="w-[220px] mx-auto rounded-3xl">
                        <img
                          src="/hero2.svg"
                          alt="Party celebration"
                          className="w-full h-[250px] object-cover rounded-3xl"
                        />
                      </div>
                    </SwiperSlide>
                    <SwiperSlide>
                      <div className="w-[220px] mx-auto rounded-3xl">
                        <img
                          src="/hero3.svg"
                          alt="Wedding event"
                          className="w-full h-[250px] object-cover rounded-3xl"
                        />
                      </div>
                    </SwiperSlide>
                  </Swiper>
                </div>

                <div className="lg:min-w-[112px] hidden md:block lg:min-h-[228px] h-[68px] w-[68px] bg-[#d7e6fe] rounded-[45px]"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
