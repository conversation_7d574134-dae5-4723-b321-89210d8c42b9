import React, { useState } from "react";

export function MarketplaceVendors() {
  // Filter states
  const [activeTab, setActiveTab] = useState("vendors");

  // Sample vendor data
  const vendors = [
    {
      id: 1,
      name: "Glitz & Glam Events",
      location: "Lekki, Lagos Nigeria",
      rating: 4.5,
      image: "/vendor-1.png",
      tags: ["Makeup", "Nail Fixing"],
    },
    {
      id: 2,
      name: "Perfect Touch Decor",
      location: "Lekki, Lagos Nigeria",
      rating: 4.5,
      image: "/vendor-2.png",
      tags: ["Event Planning", "Decoration"],
    },
    {
      id: 3,
      name: "Dreamscape Planners",
      location: "Lekki, Lagos Nigeria",
      rating: 4.5,
      image: "/vendor-3.png",
      tags: ["Event Planning", "Decoration"],
    },
    {
      id: 4,
      name: "Jollof & Beyond",
      location: "Lekki, Lagos Nigeria",
      rating: 4.5,
      image: "/vendor-4.jpg",
      tags: ["Food", "Catering"],
    },
    {
      id: 5,
      name: "Tasty Treats NG",
      location: "Lekki, Lagos Nigeria",
      rating: 4.3,
      image: "/vendor-5.png",
      tags: ["Snacks", "Drinks"],
    },
    {
      id: 6,
      name: "Ninja Bites Catering",
      location: "Lekki, Lagos Nigeria",
      rating: 4.6,
      image: "/vendor-6.jpg",
      tags: ["Pastries", "Catering"],
    },
    {
      id: 7,
      name: "Capture Moments",
      location: "Lekki, Lagos Nigeria",
      rating: 4.7,
      image: "/vendor-7.jpg",
      tags: ["Pictures", "Photography"],
    },
    {
      id: 8,
      name: "Lens Masters",
      location: "Lekki, Lagos Nigeria",
      rating: 4.9,
      image: "/vendor-1.png",
      tags: ["Photography", "Videography"],
    },
    {
      id: 9,
      name: "Elegant Steps",
      location: "Lekki, Lagos Nigeria",
      rating: 4.4,
      image: "/vendor-2.png",
      tags: ["Fashion", "Shoes"],
    },
  ];

  const lineargradient = "linear-gradient(180deg, #FCFCFD, #F8F8FA,#F6F6F9)";

  return (
    <div className="bg-white">
      <div className="container mx-auto max-w-7xl">
        {/* Tabs */}

        {/* Content Section with Border */}
        <div className="flex border border-b-0 border-[#F2F2F7] rounded-lg">
          {/* Left Side - Filter Categories */}
          <div
            style={{ background: lineargradient }}
            className="w-1/4 border-r border-b-0 pt-[80px] border-[#F2F2F7]"
          >
            <div>
              <div className="py-4 px-6 border-b border-[#F2F2F7]">
                <div className="flex justify-between items-center">
                  <span className="text-[#8E8E93] text-xs uppercase tracking-wider">
                    CATEGORY
                  </span>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6 12L10 8L6 4"
                      stroke="#8E8E93"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              </div>

              <div className="py-4 px-6 border-b border-[#F2F2F7]">
                <div className="flex justify-between items-center">
                  <span className="text-[#8E8E93] text-xs uppercase tracking-wider">
                    LOCATION
                  </span>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6 12L10 8L6 4"
                      stroke="#8E8E93"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              </div>

              <div className="py-4 px-6 border-b border-[#F2F2F7]">
                <div className="flex justify-between items-center">
                  <span className="text-[#8E8E93] text-xs uppercase tracking-wider">
                    PRICE RANGE
                  </span>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6 12L10 8L6 4"
                      stroke="#8E8E93"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              </div>

              <div className="px-6 py-4">
                <div className="flex justify-between items-center">
                  <span className="text-[#8E8E93] text-xs uppercase tracking-wider">
                    RATINGS
                  </span>
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6 12L10 8L6 4"
                      stroke="#8E8E93"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Vendor Cards */}
          <div className="p-6 pt-0 w-3/4">
            <div className="flex justify-start border-b  mb-6 h-[56px] border-b-[#F2F2F7]">
              <div className="flex relative items-center">
                <button
                  className={`px-6 py-2 text-sm font-medium relative ${
                    activeTab === "vendors"
                      ? "bg-[#4D55F2] rounded-full text-white"
                      : "text-[#8E8E93]"
                  }`}
                  onClick={() => setActiveTab("vendors")}
                >
                  Vendors
                  {activeTab === "vendors" && (
                    <>
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-[#4D55F2] rounded-full"></div>
                    </>
                  )}
                </button>
                <button
                  className={`px-6 py-2 text-sm font-medium relative ${
                    activeTab === "items"
                      ? "bg-[#4D55F2] text-white rounded-full"
                      : "text-[#8E8E93]"
                  }`}
                  onClick={() => setActiveTab("items")}
                >
                  Items
                  {activeTab === "items" && (
                    <>
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-[#4D55F2] rounded-full"></div>
                    </>
                  )}
                </button>
              </div>
            </div>
            <h2 className="mb-6 text-xl font-medium">Explore Vendors</h2>

            {/* Vendor Grid */}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {vendors.map((vendor) => (
                <div
                  key={vendor.id}
                  className=" rounded-[14px] overflow-hidden border border-[#F2F2F7] h-[360px] relative"
                >
                  {/* Vendor Image */}
                  <div className="relative h-[full min-h-[360px]">
                    <img
                      src={vendor.image}
                      alt={vendor.name}
                      className="object-cover w-full h-full min-h-[360px]"
                    />
                    {/* Circle with Icon Overlay */}
                    <div className="flex absolute top-3 left-3 justify-center items-center w-8 h-8 rounded-full backdrop-blur-sm bg-white/20">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z"
                          stroke="white"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M20.5899 22C20.5899 18.13 16.7399 15 11.9999 15C7.25991 15 3.40991 18.13 3.40991 22"
                          stroke="white"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                    {/* Tags Overlay */}
                    <div className="flex absolute bottom-3 left-3 gap-2">
                      {vendor.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="bg-black/30 backdrop-blur-sm text-white text-[10px] px-3 py-1 rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Vendor Info */}
                  <div className="p-3 h-[109px] rounded-md bg-white absolute bottom-0 w-full">
                    <div className="flex justify-between items-start">
                      <h3 className="text-base font-medium text-black">
                        {vendor.name}
                      </h3>
                      <div className="flex px-[8px] py-1 rounded-full bg-[#F2F2F7] items-center">
                        <svg
                          width="14"
                          height="13"
                          viewBox="0 0 14 13"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M6.42937 0.756229C6.60898 0.203442 7.39102 0.203444 7.57063 0.75623L8.6614 4.11327C8.74173 4.36049 8.9721 4.52786 9.23204 4.52786H12.7618C13.3431 4.52786 13.5847 5.27163 13.1145 5.61327L10.2588 7.68804C10.0485 7.84083 9.96055 8.11165 10.0409 8.35886L11.1316 11.7159C11.3113 12.2687 10.6786 12.7284 10.2083 12.3867L7.35267 10.312C7.14238 10.1592 6.85762 10.1592 6.64733 10.312L3.79166 12.3867C3.32143 12.7284 2.68874 12.2687 2.86835 11.7159L3.95912 8.35886C4.03945 8.11165 3.95145 7.84083 3.74116 7.68804L0.885485 5.61327C0.415257 5.27163 0.656924 4.52786 1.23816 4.52786H4.76796C5.0279 4.52786 5.25827 4.36049 5.3386 4.11327L6.42937 0.756229Z"
                            fill="#FF9500"
                          />
                        </svg>

                        <span className="text-[black] text-[14px] font-bold font-medium ml-1">
                          {vendor.rating}
                        </span>
                      </div>
                    </div>
                    <p className="text-xs font-light text-[#8E8E93] mt-1">
                      {vendor.location}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Load More Button */}
            <div className="flex justify-center mt-10">
              <button className=" border border-[#E5E5EA] text-[#000000] font-medium py-2 px-6 rounded-full bg-[#F2F2F7] hover:bg-[#F2F2F7]/80 transition-colors text-sm">
                Load More
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
