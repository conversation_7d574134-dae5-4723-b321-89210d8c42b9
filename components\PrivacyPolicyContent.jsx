import React, { useState, useEffect } from "react";
import { Footer } from "./Footer";
import { applyPxDefaults } from "framer-motion";
import { ContactSection } from "./ContactSection";

export function PrivacyPolicyContent() {
  const [activeSection, setActiveSection] = useState("introduction");

  useEffect(() => {
    // Function to handle hash change
    const handleHashChange = () => {
      const hash = window.location.hash.replace("#", "");
      if (hash) {
        setActiveSection(hash);
      }
    };

    // Function to handle scroll
    const handleScroll = () => {
      const sections = document.querySelectorAll("section[id]");
      let currentSection = "introduction";

      sections.forEach((section) => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        if (
          window.scrollY >= sectionTop - 100 &&
          window.scrollY < sectionTop + sectionHeight - 100
        ) {
          currentSection = section.getAttribute("id");
        }
      });

      setActiveSection(currentSection);
    };

    // Add event listeners
    window.addEventListener("hashchange", handleHashChange);
    window.addEventListener("scroll", handleScroll);

    // Initial check
    handleHashChange();

    // Cleanup
    return () => {
      window.removeEventListener("hashchange", handleHashChange);
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <div className="w-full">
      {/* Hero Section */}
      <div
        className="relative w-full h-[540px] md:h-[850px] pt-16 flex flex-col items-center justify-start  after:content-[''] after:absolute after:bottom-0 after:bg-gradient-to-b after:w-full after:h-[80px] after:from-[#FFFFFF] after:to-[#fff] after:bottom-[-80px] from-[#FFFFFF] to-[#fff] "
        style={{
          backgroundImage:
            "linear-gradient(rgba(235, 243, 254, 0.85)), url('/privacy-hero.jpg')",
          backgroundSize: "cover",
          backgroundPosition: "left",
          backgroundBlendMode: "hard-light",
        }}
      >
        <div className="flex justify-center items-center mb-8">
          <div className="flex items-center">
            <img src="/logo.svg" alt="EventPark" className="h-6" />
            <span className="text-[#4D55F2] font-bold text-[24px] ml-2">
              EventPark.
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="relative z-10 px-4 mt-[40px] max-w-4xl text-center">
          <p className="text-[#000F4A] uppercase tracking-wider mb-6 text-[18px]">
            PRIVACY POLICY
          </p>
          <h1 className="mb-4 text-5xl font-bold">
            <span className="text-[#5075AF] text-[48px] md:text-[100px]">
              Your Privacy,
            </span>
            <br />
            <span className="text-[#000073] text-[48px] md:text-[130px]">
              Our Priority
            </span>
          </h1>
          <p className="text-[#8E8E93] md:max-w-[400px] mx-auto mb-3 md:mb-6 text-[18px] md:text-[24px]">
            We protect your data with utmost transparency and care.
          </p>
          <p className="text-xs flex items-center w-fit mx-auto text-[black] bg-[#BAD4FD] font-bold rounded-[30px] gap-1 px-3 py-1">
            <span className="text-[black] w-4 h-4">•</span>{" "}
            <span className="text-[black] font-medium">Last updated:</span>{" "}
            <span className="text-[black]">10 January 2024</span>
          </p>
        </div>
      </div>

      <div className="">
        <div className="mx-auto max-w-7xl mt-[-100px] relative z-[100]">
          <nav className="flex justify-center">
            <ul className="flex px-8 py-3 space-x-12 bg-white rounded-full shadow-lg">
              <li>
                <a
                  href="/"
                  className="text-[10px] md:text-sm font-medium text-black hover:text-[#4D55F2]"
                >
                  HOME
                </a>
              </li>
              {/* <li>
                <a
                  href="#"
                  className="text-[10px] md:text-sm font-medium text-black hover:text-[#4D55F2]"
                >
                  COMPANY
                </a>
              </li> */}
              <li>
                <a
                  href="/vendor"
                  className="text-[10px] md:text-sm font-medium text-black hover:text-[#4D55F2]"
                >
                  FOR VENDORS
                </a>
              </li>
              <li>
                <a
                  href="/marketplace"
                  className="text-[10px] md:text-sm font-medium text-black hover:text-[#4D55F2]"
                >
                  MARKETPLACE
                </a>
              </li>
            </ul>
          </nav>
        </div>
      </div>
      <div
        className="h-[80px] w-full bg-cover bg-no-repeat"
        style={{ backgroundImage: "url('/terms-rectangle.svg')" }}
      ></div>

      {/* Main Content */}
      <div className="container px-4 py-16 mt-[200px\] mx-auto max-w-7xl">
        <div className="flex flex-col gap-12 md:flex-row">
          {/* Sidebar */}
          <div className="w-full md:w-1/5">
            {/* <div className="text-[#4D55F2] uppercase text-sm">
              <span>Contents</span>
            </div> */}
            <nav className="sticky top-8">
              <ul className="p-4 space-y-4 text-xs border border-t-0 border-gray-100">
                {[
                  { id: "introduction", label: "Introduction" },
                  {
                    id: "information-collect",
                    label: "Information We Collect",
                  },
                  {
                    id: "use-information",
                    label: "How We Use Your Information",
                  },
                  {
                    id: "share-information",
                    label: "How We Share Your Information",
                  },
                  {
                    id: "protect-information",
                    label: "How We Protect Your Information",
                  },
                  { id: "privacy-rights", label: "Your Privacy Rights" },
                  { id: "data-retention", label: "Data Retention" },
                  { id: "third-party", label: "Third-Party Links" },
                  { id: "childrens-privacy", label: "Children's Privacy" },
                  { id: "changes", label: "Changes to This Privacy Policy" },
                  { id: "contact", label: "Contact Us" },
                ].map((item, index, array) => (
                  <li
                    key={item.id}
                    className={`py-2 ${
                      index < array.length - 1 ? "border-b border-gray-100" : ""
                    }`}
                  >
                    <a
                      href={`#${item.id}`}
                      className={`block ${
                        activeSection === item.id
                          ? "text-white font-medium bg-[#4D55F2] rounded-full px-3 py-1 h-[40px] flex items-center"
                          : "text-[#666666] hover:text-[#4D55F2] px-3 py-1"
                      }`}
                      onClick={() => setActiveSection(item.id)}
                    >
                      {item.label}
                    </a>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* Privacy Policy Content */}
          <div className="py-8 w-full border border-t-0 border-gray-100 md:w-3/4">
            <section id="introduction" className="px-8 pb-8 mb-12">
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Introduction
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                At EventPark, your privacy is our priority. This Privacy Policy
                explains how we collect, use, share, and protect your personal
                information when you use our platform. By using EventPark, you
                agree to the terms outlined in this Privacy Policy.
              </p>
            </section>

            <section
              id="information-collect"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Information We Collect
              </h2>

              <h3 className="mb-2 text-xl font-semibold text-black">
                1. Information You Provide
              </h3>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed mb-6">
                <li>
                  <strong>Account Information:</strong> When you create an
                  account, we collect your name, email address, phone number,
                  and password.
                </li>
                <li>
                  <strong>Vendor Details:</strong> Vendors may provide business
                  names, descriptions, services offered, pricing, and portfolio
                  details.
                </li>
                <li>
                  <strong>Payment Information:</strong> To process transactions,
                  we may collect billing addresses and payment details (via
                  secure third-party payment processors).
                </li>
              </ul>

              <h3 className="mb-2 text-xl font-semibold text-black">
                2. Information We Collect Automatically
              </h3>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed mb-6">
                <li>
                  <strong>Usage Data:</strong> Information about your
                  interactions with our platform, such as pages viewed, searches
                  performed, and bookings made.
                </li>
                <li>
                  <strong>Device Information:</strong> Details like IP address,
                  browser type, device type, and operating system.
                </li>
                <li>
                  <strong>Cookies and Tracking Technologies:</strong> We use
                  cookies to enhance your experience and gather analytics data.
                </li>
              </ul>

              <h3 className="mb-2 text-xl font-semibold text-black">
                3. Information from Third Parties
              </h3>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>
                  Data from payment processors or marketing partners to
                  facilitate transactions or improve our services.
                </li>
              </ul>
            </section>

            <section
              id="use-information"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                How We Use Your Information
              </h2>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>To provide and improve our platform services.</li>
                <li>
                  To process bookings, payments, and communication between event
                  planners and vendors.
                </li>
                <li>
                  To personalise your experience and show relevant vendors or
                  services.
                </li>
                <li>
                  To send updates, promotional content, and important notices.
                </li>
                <li>
                  To ensure compliance with legal obligations and protect
                  against fraudulent activities.
                </li>
              </ul>
            </section>

            <section
              id="share-information"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                How We Share Your Information
              </h2>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>
                  <strong>With Vendors and Event Planners:</strong> To
                  facilitate bookings and communication.
                </li>
                <li>
                  <strong>With Service Providers:</strong> Third-party providers
                  for payment processing, analytics, and marketing support.
                </li>
                <li>
                  <strong>For Legal Compliance:</strong> If required by law or
                  to enforce our terms and policies.
                </li>
                <li>
                  <strong>With Your Consent:</strong> When you explicitly agree
                  to share your information.
                </li>
              </ul>
            </section>

            <section
              id="protect-information"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                How We Protect Your Information
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                We implement industry-standard security measures to protect your
                data, including:
              </p>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>Encryption for sensitive information.</li>
                <li>Regular monitoring for vulnerabilities.</li>
                <li>Restricted access to personal data.</li>
              </ul>
              <p className="text-[#666666] mt-4 text-sm leading-relaxed">
                However, no system is completely secure. Please use strong
                passwords and notify us immediately of any unauthorized account
                access.
              </p>
            </section>

            <section
              id="privacy-rights"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Your Privacy Rights
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                Depending on your location, you may have rights such as:
              </p>
              <ul className="list-disc pl-6 text-[#666666] space-y-2 text-sm leading-relaxed">
                <li>Accessing or updating your personal data.</li>
                <li>Requesting deletion of your data.</li>
                <li>Opting out of marketing communications.</li>
                <li>Restricting or objecting to data processing.</li>
              </ul>
              <p className="text-[#666666] mt-4 text-sm leading-relaxed">
                To exercise your rights, contact us at{" "}
                <a href="mailto:<EMAIL>" className="text-[#4D55F2]">
                  <EMAIL>
                </a>
              </p>
            </section>

            <section
              id="data-retention"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Data Retention
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                We retain your data as long as necessary to provide our
                services, comply with legal obligations, resolve disputes, and
                enforce our agreements. Once no longer needed, your data will be
                deleted or anonymized.
              </p>
            </section>

            <section
              id="third-party"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Third-Party Links
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                Our platform may include links to third-party websites or
                services. We are not responsible for their privacy practices.
                Please review their policies before sharing any information.
              </p>
            </section>

            <section
              id="childrens-privacy"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Children's Privacy
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                EventPark is not intended for individuals under 18 years old. We
                do not knowingly collect personal information from minors.
              </p>
            </section>

            <section
              id="changes"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Changes to This Privacy Policy
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                We may update this Privacy Policy periodically. Significant
                changes will be communicated via email or platform
                notifications. Continued use of our platform after updates
                constitutes acceptance of the revised policy.
              </p>
            </section>

            <section
              id="contact"
              className="px-8 pt-8 pb-8 mb-12 border-t border-gray-100"
            >
              <h2 className="text-[40px] font-bold mb-4 text-black">
                Contact Us
              </h2>
              <p className="text-[#666666] mb-4 text-sm leading-relaxed">
                If you have any questions or concerns about this Privacy Policy,
                please contact us at:
              </p>
              <p className="text-[#666666] text-sm mb-2">
                <strong>EventPark Support Team</strong>
              </p>
              <p className="text-[#666666] text-sm mb-2">
                <strong>Email:</strong> <EMAIL>
              </p>
              <p className="text-[#666666] text-sm">
                <strong>Mobile Number:</strong> +234 800 EVENT PARK
              </p>
            </section>
          </div>
        </div>
      </div>

      {/* Contact Section */}

      <Footer />
    </div>
  );
}
