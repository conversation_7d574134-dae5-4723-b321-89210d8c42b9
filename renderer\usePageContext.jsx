// renderer/usePageContext.jsx
import React, { useContext } from 'react';

// Create a context to hold the pageContext
const PageContext = React.createContext(undefined);

// Provider component that wraps the app and provides the pageContext
export function PageContextProvider({ pageContext, children }) {
  return (
    <PageContext.Provider value={pageContext}>
      {children}
    </PageContext.Provider>
  );
}

// Hook to use the pageContext
export function usePageContext() {
  const pageContext = useContext(PageContext);
  
  // Ensure the hook is used within a PageContextProvider
  if (pageContext === undefined) {
    throw new Error('usePageContext() must be used within a PageContextProvider');
  }
  
  return pageContext;
}
