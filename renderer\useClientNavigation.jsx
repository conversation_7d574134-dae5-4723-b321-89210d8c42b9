// renderer/useClientNavigation.jsx
import { useCallback, useEffect } from "react";

// Global event for navigation start
export const NAVIGATION_EVENT = "vike-navigation-start";

// Initialize navigation listeners immediately
if (typeof window !== "undefined") {
  initializeNavigationListeners();
}

// Function to initialize navigation listeners
function initializeNavigationListeners() {
  const handleLinkClick = (event) => {
    // Only handle left clicks with no modifier keys
    if (
      event.button !== 0 ||
      event.metaKey ||
      event.ctrlKey ||
      event.shiftKey ||
      event.altKey
    ) {
      return;
    }

    const link = event.target.closest("a");
    if (!link) return;

    const href = link.getAttribute("href");
    if (!href) return;

    // Only handle internal links
    if (!href.startsWith("/#") && !href.startsWith("/api/")) {
      event.preventDefault();

      // Dispatch a custom event to signal navigation start
      window.dispatchEvent(
        new CustomEvent(NAVIGATION_EVENT, { detail: { url: href } })
      );

      // Delay actual navigation slightly to allow progress bar to start
      setTimeout(() => {
        window.location.href = href;
      }, 50);
    }
  };

  // Add click event listeners to all internal links
  document.addEventListener("click", handleLinkClick);
}

// Custom hook for client-side navigation
export function useClientNavigation() {
  // Function to navigate to a new page
  const navigate = useCallback((url) => {
    // Dispatch a custom event to signal navigation start
    window.dispatchEvent(
      new CustomEvent(NAVIGATION_EVENT, { detail: { url } })
    );

    // Delay actual navigation slightly to allow progress bar to start
    setTimeout(() => {
      window.location.href = url;
    }, 50);
  }, []);

  return { navigate };
}
