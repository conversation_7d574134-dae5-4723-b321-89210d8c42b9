// renderer/PageLayout.jsx
import React, { useState, useEffect } from "react";
import "./index.css";
import { usePageContext } from "./usePageContext";
import { NAVIGATION_EVENT } from "./useClientNavigation";

// Navigation Progress Bar Component
function NavigationProgress() {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const pageContext = usePageContext();
  const urlPathname = pageContext.urlPathname;

  // Track if this is the initial page load
  const isInitialMount = React.useRef(true);

  // Handle navigation start event (triggered before page change)
  useEffect(() => {
    const startNavigation = () => {
      // Show the progress bar immediately when navigation starts
      setIsVisible(true);
      setProgress(0);

      // Quickly move to 30%
      setTimeout(() => setProgress(30), 50);

      // Then more slowly to 70%
      setTimeout(() => setProgress(70), 200);

      // Then even more slowly to 90%
      setTimeout(() => setProgress(90), 400);
    };

    // Listen for navigation start event
    if (typeof window !== "undefined") {
      window.addEventListener(NAVIGATION_EVENT, startNavigation);

      return () => {
        window.removeEventListener(NAVIGATION_EVENT, startNavigation);
      };
    }
  }, []);

  // Handle page load/change (triggered after page loads)
  useEffect(() => {
    // Don't show the progress bar on initial page load
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Complete the progress when the component mounts with new URL (page has loaded)
    const timer = setTimeout(() => {
      setProgress(100);

      // Hide after the completion animation
      setTimeout(() => {
        setIsVisible(false);
        setProgress(0);
      }, 300);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, [urlPathname]); // Re-run when the path changes

  return (
    <div
      className={`fixed top-0 left-0 right-0 h-1 z-50 transition-opacity duration-300 ${
        isVisible ? "opacity-100" : "opacity-0"
      }`}
    >
      <div
        className="h-full bg-gradient-to-r from-[#FFBBA3] to-[#4D55F2] transition-all duration-300 ease-out"
        style={{ width: `${progress}%` }}
      />
    </div>
  );
}

export function PageLayout({ children }) {
  // Initialize client-side navigation
  useEffect(() => {
    // Only run in browser environment
    if (typeof window !== "undefined") {
      // Dynamically import to avoid SSR issues
      import("./useClientNavigation").then(() => {
        // Just importing the module will set up the navigation listeners
        console.log("Client navigation initialized");
      });
    }
  }, []);

  return (
    <div className="min-h-screen">
      <NavigationProgress />
      {children}
    </div>
  );
}
